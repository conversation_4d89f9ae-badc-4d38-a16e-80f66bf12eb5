﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

// #define DEBUG_PRINT
using System.Reactive;
using System.Text;
using NetFabric.Hyperlinq;
using Nncase.CodeGen.NTT;
using Nncase.IR;
using Nncase.Targets;
using Nncase.TIR;
using Nncase.Utilities;
using Razor.Templating.Core;

namespace Nncase.CodeGen.XPU;

public class DeviceCSourceConvertVisitor : Nncase.CodeGen.NTT.DeviceCSourceConvertVisitor
{
    public DeviceCSourceConvertVisitor(NTTTargetOptions targetOptions)
        : base()
    {
        TargetOptions = targetOptions;
    }

    public NTTTargetOptions TargetOptions { get; }

    /// <inheritdoc/>
    protected override NTT.CSymbol VisitPrimFunction(PrimFunction expr)
    {
        if (_exprMemo.TryGetValue(expr, out var symbol))
        {
            return symbol;
        }

        if (expr.CheckedType is not CallableType { ReturnType: TupleType r } || r != TupleType.Void)
        {
            throw new NotSupportedException("The PrimFunction must return void!");
        }

        var ctype = $"template<{string.Join(", ", Enumerable.Range(0, expr.Parameters.Length).Select(x => $"class T{x}"))}>" +
            $"void {expr.Name}({string.Join(", ", expr.Parameters.AsValueEnumerable().Select(Visit).Select((s, i) => $"T{i} &&{s.Name}").ToArray())}, const thread_context &ctx)";

        using (var scope = new NTT.IndentScope(_deviceBuilder))
        {
            // 1. Function signature
            NTT.IndentScope.Writer.IndWrite($"{ctype} {{\n");
            NTT.IndentScope.Writer.IndWrite($"bool loaded = false;\n");
            var sysMod = CSourceUtilities.GetRuntimeMode();
            if (sysMod == RuntimeMode.SystemMode)
            {
                NTT.IndentScope.Writer.IndWrite($"int block_id = device_block_id();\n");
            }

            // 2. Function body
            using (_ = new IndentScope())
            {
                Visit(expr.Body);
            }

            // 3. Function closing
            NTT.IndentScope.Writer.IndWrite("}\n");
        }

        symbol = new(ctype, expr.Name);
        _exprMemo.Add(expr, symbol);
        return symbol;
    }

    protected override NTT.CSymbol VisitLet(Let expr)
    {
        if (_exprMemo.TryGetValue(expr, out var symbol))
        {
            return symbol;
        }

        var @var = Visit(expr.Var);
        var value = Visit(expr.Expression);
        _exprMemo[(BaseExpr)expr.Var] = new(value.Type, @var.Name);

#if DEBUG_PRINT
        NTT.IndentScope.Writer.IndWrite($"runtime_util->printf(\"let {@var.Name}\\n\");\n");
#endif
        if (value.Type.StartsWith("array"))
        {
            var ss = value.Type.Split(" ");
            IndentScope.Writer.IndWrite($"{ss[1]} {@var.Name}[{ss[2]}];\n");
        }
        else if (expr.Expression is TIR.Buffer buffer)
        {
            var dimensions = buffer.Dimensions;
            var dimensionValues = dimensions.AsValueEnumerable().Select(x => Visit(x).Name);
            var strideValues = buffer.Strides.AsValueEnumerable().Select(x => Visit(x).Name);

            // TODO: reopen buffer optimization
            var sysMod = CSourceUtilities.GetRuntimeMode();
            var tid = sysMod == RuntimeMode.SystemMode && buffer.DistributedType!.AxisPolicies.Any(p => p is SBPSplit s && s.Axes.Contains(buffer.DistributedType!.Placement.Rank - 1)) ? "ctx.tid()" : "ctx.tid()";
            if (sysMod == RuntimeMode.SystemMode)
            {
                NTT.IndentScope.Writer.IndWrite($"auto {@var.Name} = make_tensor_view(std::span<{buffer.ElemType.ToC()}, {buffer.MemSpan.Size} / {buffer.ElemType.SizeInBytes}>(({buffer.ElemType.ToC()}*)(sram[block_id] + {tid} * sram_size_per_thread + {buffer.MemSpan.Start}), {buffer.MemSpan.Size} / {buffer.ElemType.SizeInBytes}), make_shape({StringUtility.Join(", ", dimensionValues)}), make_strides({StringUtility.Join(", ", strideValues)}));\n");
            }
            else
            {
                NTT.IndentScope.Writer.IndWrite($"auto {@var.Name} = make_tensor_view(std::span<{buffer.ElemType.ToC()}, {buffer.MemSpan.Size} / {buffer.ElemType.SizeInBytes}>(({buffer.ElemType.ToC()}*)(sram[ctx.cid() * PLACEMENTS[ctx.placement()][1] * PLACEMENTS[ctx.placement()][2] * PLACEMENTS[ctx.placement()][3] + ctx.did() * PLACEMENTS[ctx.placement()][2] * PLACEMENTS[ctx.placement()][3] + ctx.yid() * PLACEMENTS[ctx.placement()][3]  + ctx.xid()] + {tid} * sram_size_per_thread + {buffer.MemSpan.Start}), {buffer.MemSpan.Size} / {buffer.ElemType.SizeInBytes}), make_shape({StringUtility.Join(", ", dimensionValues)}), make_strides({StringUtility.Join(", ", strideValues)}));\n");
            }
        }
        else
        {
            NTT.IndentScope.Writer.IndWrite($"{value.Type} {@var.Name} = {value.Name};\n");
        }

        Visit(expr.Body);

        symbol = new(string.Empty, string.Empty);
        _exprMemo.Add(expr, symbol);
        return symbol;
    }

    protected override CSymbol VisitBuffer(TIR.Buffer expr)
    {
        if (_exprMemo.TryGetValue(expr, out var symbol))
        {
            return symbol;
        }

        // var dimensions = expr.DistributedType is null ? expr.Dimensions : ((RankedShape)expr.DistributedType.TensorType.Shape).Dimensions;
        var dimensions = expr.Dimensions;
        var isFixedDimensions = dimensions.AsValueEnumerable().All(x => x.IsFixed);
        var isFixedStrides = expr.Strides.AsValueEnumerable().All(x => x.IsFixed);
        var dimensionSymbols = dimensions.AsValueEnumerable().Select(Visit).ToArray();
        var strideSymbols = expr.Strides.AsValueEnumerable().Select(Visit).ToArray();

        var dtypeStr = expr.ElemType.ToC();
        var dimensionStr = KernelUtility.DimensionsToC(isFixedDimensions, dimensionSymbols, true);
        var strideStr = KernelUtility.StridesToC(isFixedStrides, strideSymbols, true);
        var type = $"tensor_view<{dtypeStr}, {dimensionStr}, {strideStr}> ";

        symbol = new(type, expr.Name);
        _exprMemo.Add(expr, symbol);
        return symbol;
    }

    protected override CSymbol VisitCall(Call expr)
    {
        if (_exprMemo.TryGetValue(expr, out var symbol))
        {
            return symbol;
        }

        string type = expr.CheckedType switch
        {
            TupleType x when x == TupleType.Void => string.Empty,
            TensorType { IsScalar: true } x => x.DType.ToC(),
            TensorType or DistributedType => "auto",
            _ => throw new NotSupportedException(),
        };

        string str = string.Empty;
        var arguments = expr.Arguments.AsValueEnumerable().Select(Visit).ToArray();
        switch (expr.Target)
        {
            case PrimFunction deviceFunc:
                IndentScope.Writer.IndWrite($"{deviceFunc.Name}({string.Join(",", arguments.Select(arg => arg.Name))});\n");
                break;
            case IR.Math.Binary op:
                str = CSourceUtilities.ConvertBinary(op, arguments);
                break;
            case IR.Math.Unary op:
                str = CSourceUtilities.ConvertUnary(op, arguments);
                break;
            case IR.Math.Compare op:
                str = CSourceUtilities.ConvertCompare(op, arguments);
                break;
            case IR.Math.Select op:
                str = CSourceUtilities.ConvertSelect(op, arguments);
                break;
            case IR.Shapes.AsTensor op:
                str = arguments[0].Name;
                break;
            case TIR.NTT.SramPtr op:
                str = $"g_cpu_mt->sram_address(bid, tid) + {arguments[0].Name}";
                break;
            case TIR.Load op:
                str = $"{arguments[0].Name}[{arguments[1].Name}]";
                break;
            case TIR.Store op:
#if DEBUG_PRINT
                IndentScope.Writer.IndWrite($"runtime_util->printf(\"{arguments[0].Name}[%d]\\n\", {arguments[1].Name});\n");
#endif
                IndentScope.Writer.IndWrite($"{arguments[0].Name}[{arguments[1].Name}] = {arguments[2].Name};\n");
                break;
            case TIR.NTT.PtrOf op:
                str = op.PtrName + ".data()";
                break;
            case IR.Buffers.Allocate op:
                if (op.Malloc)
                {
                    str = $"({type})runtime_util->malloc({arguments[0].Name})";
                }
                else
                {
                    type = $"array {((PointerType)expr.CheckedDataType).ElemType.ToC()} {arguments[0].Name}";
                    str = $"";
                }

                break;
            case IR.Buffers.BufferSubview op:
                {
                    var arg0 = VisitDimOrShape(expr.Arguments[1], CShapeKind.Shape).Name;
                    var arg1 = VisitDimOrShape(expr.Arguments[2], CShapeKind.Shape).Name;
                    str = $"{arguments[0].Name}.view({arg0}, {arg1})";
                }

                break;
            case IR.Buffers.AllocateBufferView op:
                {
                    var buffer = (TIR.Buffer)expr.Arguments[0];
                    var dimensions = buffer.DistributedType is null ? buffer.Dimensions : ((RankedShape)buffer.DistributedType.TensorType.Shape).Dimensions;
                    var isFixedDimensions = dimensions.AsValueEnumerable().All(x => x.IsFixed);
                    var isFixedStrides = buffer.Strides.AsValueEnumerable().All(x => x.IsFixed);
                    var dimensionSymbols = dimensions.AsValueEnumerable().Select(Visit).ToArray();
                    var strideSymbols = buffer.Strides.AsValueEnumerable().Select(Visit).ToArray();

                    var dtypeStr = buffer.ElemType.ToC();
                    var dimensionStr = KernelUtility.DimensionsToC(isFixedDimensions, dimensionSymbols, false);
                    var strideStr = KernelUtility.StridesToC(isFixedStrides, strideSymbols, false);
                    str = $"{{span_cast<{dtypeStr}>({Visit(buffer.MemSpan).Name}), {dimensionStr}, {strideStr}}}";
                }

                break;
            case IR.Tensors.Cast op:
                str = $"(({op.NewType.ToC()}){arguments[0].Name})";
                break;
            case TIR.Memcopy op:
                {
                    // TODO: support fusion, only consider single matmul here
                    var sysMod = CSourceUtilities.GetRuntimeMode();
                    var matmul = ExprCollector.Collect(VisitEntry).Where(e => e is Call { Target: TIR.NTT.Matmul }).FirstOrDefault();
                    if (sysMod == RuntimeMode.SystemMode)
                    {
                        if (matmul is not null && expr.Arguments[0].CheckedDataType is VectorType vt && vt.Lanes.Count == 2)
                        {
                            if (expr.Arguments[0] is Var)
                            {
                                // var distributedType = ((TIR.Buffer)((Call)expr.Arguments[1]).Arguments[0]).DistributedType;

                                // if (distributedType?.NdSBP[^1] == SBP.B)
                                // {
                                //     IndentScope.Writer.IndWrite($"if (ctx.tid() == 0) {{\n");
                                // }
                                IndentScope.Writer.IndWrite(@$"if (!loaded)
        global_hardware_ctx.tdma_acquire_lock(ctx);" + "\n");
                                IndentScope.Writer.IndWrite($"tensor_copy_tdma<true>({arguments[1].Name}, {arguments[0].Name});\n");
                                IndentScope.Writer.IndWrite($"loaded = true;\n");

                                // if (distributedType?.NdSBP[^1] == SBP.B)
                                // {
                                //     IndentScope.Writer.IndWrite($"}}\n");
                                // }
                            }
                            else
                            {
                                IndentScope.Writer.IndWrite($"tstore_wait();\n");
                                IndentScope.Writer.IndWrite($"global_hardware_ctx.tdma_acquire_lock(ctx);\n");
                                IndentScope.Writer.IndWrite($"tensor_copy_tdma<false>({arguments[1].Name}, {arguments[0].Name});\n");
                                IndentScope.Writer.IndWrite(@$"tdma_wait();" + "\n");
                                IndentScope.Writer.IndWrite($"global_hardware_ctx.tdma_release_lock(ctx);\n");
                            }
                        }
                        else
                        {
                            if (expr.Arguments[0] is Var)
                            {
                                IndentScope.Writer.IndWrite($"tensor_copy_tdma<true, false>({arguments[1].Name}, {arguments[0].Name});\n");
                            }
                            else
                            {
                                IndentScope.Writer.IndWrite($"tensor_copy_tdma<false, false>({arguments[1].Name}, {arguments[0].Name});\n");
                            }
                        }
                    }
                    else
                    {
                        IndentScope.Writer.IndWrite($"tensor_copy({arguments[1].Name}, {arguments[0].Name});\n");
                    }
                }

                break;
            case TIR.NTT.Unary op:
                IndentScope.Writer.IndWrite(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Unary.cshtml", new UnaryKernelTemplateModel
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    UnaryOp = op.UnaryOp,
                }).Result);
                break;
            case TIR.NTT.Binary op:
                IndentScope.Writer.IndWrite(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Binary.cshtml", new BinaryKernelTemplateModel
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    BinaryOp = op.BinaryOp,
                }).Result);
                break;
            case TIR.NTT.VectorizedBinary op:
                IndentScope.Writer.IndWrite(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Binary.cshtml", new BinaryKernelTemplateModel
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    BinaryOp = op.BinaryOp,
                }).Result);
                break;
            case TIR.NTT.Swish swish:
                if (swish.Beta == 1.0f)
                {
                    IndentScope.Writer.IndWrite($"unary<ops::swish>({arguments[0].Name}, {arguments[1].Name});\n");
                }
                else
                {
                    IndentScope.Writer.IndWrite($"\n{{\nauto b= {swish.Beta}; auto tb = make_tensor_view_from_address<float>(&b, fixed_shape_v<>);\n");
                    IndentScope.Writer.IndWrite($"binary<ops::swishb>({arguments[0].Name}, tb, {arguments[1].Name});\n}}\n");
                }

                break;
            case TIR.NTT.Matmul matmul:
                {
                    var sysMod = CSourceUtilities.GetRuntimeMode();
                    var k80 = sysMod == RuntimeMode.SystemMode && expr.Arguments[0].CheckedDataType is VectorType vt && vt.Lanes.Count == 2;
                    if (k80)
                    {
                        IndentScope.Writer.IndWrite(@$"if (loaded){{
                tdma_wait();
                global_hardware_ctx.tdma_release_lock(ctx);
                loaded = false;
            }}" + "\n");
                        IndentScope.Writer.IndWrite($"global_hardware_ctx.tmma_acquire_lock(ctx);\n");
                    }

                    IndentScope.Writer.IndWrite($@"if ({arguments[3].Name}) {{
                        matmul<true, {matmul.TransposeA.ToString().ToLower(System.Globalization.CultureInfo.CurrentCulture)}, {matmul.TransposeB.ToString().ToLower(System.Globalization.CultureInfo.CurrentCulture)}>({arguments[0].Name}, {arguments[1].Name}, {arguments[2].Name}, fixed_shape_v<{string.Join(",", matmul.LhsVectorizedAxes)}>, fixed_shape_v<>, fixed_shape_v<{string.Join(",", matmul.RhsVectorizedAxes)}>, fixed_shape_v<>);
                    }} else {{
                        matmul<false, {matmul.TransposeA.ToString().ToLower(System.Globalization.CultureInfo.CurrentCulture)}, {matmul.TransposeB.ToString().ToLower(System.Globalization.CultureInfo.CurrentCulture)}>({arguments[0].Name}, {arguments[1].Name}, {arguments[2].Name}, fixed_shape_v<{string.Join(",", matmul.LhsVectorizedAxes)}>, fixed_shape_v<>, fixed_shape_v<{string.Join(",", matmul.RhsVectorizedAxes)}>, fixed_shape_v<>);
                    }}" + "\n");

                    if (k80)
                    {
                        IndentScope.Writer.IndWrite($"global_hardware_ctx.tmma_release_lock(ctx);\n");
                    }
                }

                break;
            case TIR.NTT.Pack pack:
                IndentScope.Writer.Write(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Pack.cshtml", new TypedKernelTemplateModel<TIR.NTT.Pack>(pack)
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    Indent = new string(' ', IndentScope.Writer.Indent),
                }).Result);
                break;
            case TIR.NTT.Transpose transpose:
                IndentScope.Writer.Write(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Transpose.cshtml", new TypedKernelTemplateModel<TIR.NTT.Transpose>(transpose)
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    Indent = new string(' ', IndentScope.Writer.Indent),
                }).Result);
                break;
            case TIR.NTT.Unpack unpack:
                IndentScope.Writer.Write(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Unpack.cshtml", new TypedKernelTemplateModel<TIR.NTT.Unpack>(unpack)
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    Indent = new string(' ', IndentScope.Writer.Indent),
                }).Result);
                break;
            case TIR.NTT.Reduce reduce:
                IndentScope.Writer.Write(RazorTemplateEngine.RenderAsync("~/CodeGen/CPU/Templates/Kernels/Reduce.cshtml", new TypedKernelTemplateModel<TIR.NTT.Reduce>(reduce)
                {
                    Arguments = arguments.Select(x => new KernelArgument { Symbol = x }).ToArray(),
                    Indent = new string(' ', IndentScope.Writer.Indent),
                }).Result);
                break;
            case TIR.NTT.Cast cast:
                IndentScope.Writer.IndWrite($"cast({arguments[0].Name}, {arguments[1].Name});\n");
                break;
            default:
                throw new NotSupportedException();
        }

        symbol = new(type, str);
        _exprMemo.Add(expr, symbol);
        return symbol;
    }
}
