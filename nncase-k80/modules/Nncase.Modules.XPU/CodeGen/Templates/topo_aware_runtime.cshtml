@using System.Linq
@using NetFabric.Hyperlinq
@using Nncase
@using Nncase.CodeGen.XPU
@model Nncase.CodeGen.NTT.NTTTargetOptionsModel
@{
  var hierarchy = Model.Options.Hierarchies[0];
  var hierarchyNames = Model.Options.HierarchyNames;
  var worldSize = (int)TensorUtilities.GetProduct(hierarchy);
  var combinations = Nncase.Utilities.LinqUtility.Combination(hierarchy.Length).Select(i => i.ToArray()).ToArray();

  string GetName(IEnumerable<int> axes, string prefix = "group_") {
    return prefix + string.Join("_", Enumerable.Range(0, hierarchy.Length).Select(i => (axes.Contains(i) ? "r" : string.Empty) + Model.Options.HierarchyNames[i]));
  }
}

#pragma once
#include "thread_context.h"
#include "hardware_context.h"
#include <nncase/ntt/ntt.h>
#include <atomic>
#include <nncase/ntt/tensor.h>
#include <nncase/ntt/tensor_traits.h>

#ifdef SYS_MODE
#include "sync.h"
#define DEVICE_SEC __device__
#else
#define DEVICE_SEC
#endif
using namespace nncase::ntt;
/**
 * @@brief topology aware runtime
 * 
 */
namespace tar {
  @foreach (var comb in combinations)
  {
    var groupSize = (int)TensorUtilities.GetProduct(comb.Select(i => hierarchy[i]).ToArray());
    var groups = worldSize / groupSize;
    
    var shape = hierarchy.ToArray();
    var groupName = GetName(comb);
    foreach (var i in comb) {
      shape[i] = 1;
    }
    var groupRawName = groupName + "_raw";

@:DEVICE_SEC std::atomic<int32_t> @(groupRawName)[@(groups)][2] {
    @for (int i = 0; i < groups; i++) 
    {
  @:{std::atomic<int32_t>(@(groupSize)), std::atomic<int32_t>(@(groupSize))},
    }
@:};
@:DEVICE_SEC auto @(groupName) = nncase::ntt::make_tensor_view_from_address(@(groupRawName), nncase::ntt::fixed_shape_v<@(string.Join(",", shape))>);
@:
  }

@if (Model.CollectivePoolSize > 0) {
@:  DEVICE_SEC uint8_t collective_pool_ptr[@Model.CollectivePoolSize];
} else {
@:  DEVICE_SEC uint8_t collective_pool_ptr[1];
}
enum reduce_kind {
@foreach(var comb in combinations) {
@:  @(GetName(comb, string.Empty)) = @Html.Raw(string.Join(" | ", comb.Select(axis => $"(1 << {hierarchy.Length - axis})"))),
}
};

constexpr std::array<size_t, @(hierarchy.Length)> Hierarchy = {@(string.Join(", ", hierarchy))};
DEVICE_SEC auto src_ptr_tensor = nncase::ntt::make_tensor<void *>(nncase::ntt::fixed_shape_v<@(string.Join(", ", hierarchy))>);
DEVICE_SEC auto dest_ptr_tensor = nncase::ntt::make_tensor<void *>(nncase::ntt::fixed_shape_v<@(string.Join(", ", hierarchy))>);
}

void arrive_and_wait(std::atomic<int32_t> vars[2], int32_t value = 0)
{
    vars[0].fetch_add(-1, std::memory_order_seq_cst);
    while (vars[0].load() != 0)
        ;

    vars[1].fetch_add(-1, std::memory_order_seq_cst);
    while (vars[1].load() != 0)
        ;

    vars[0].fetch_add(1, std::memory_order_seq_cst);
    while (vars[0].load() != value)
        ;

    vars[1].fetch_add(1, std::memory_order_seq_cst);
    while (vars[1].load() != value)
        ;    
}

/**
 * @@brief topology aware collective
 * 
 */
namespace tac {

using namespace nncase;

template <ntt::Shape GlobalShape, ntt::Shape Index, ntt::Tensor TDst>
void tensor_boxing_load_sync(const GlobalShape &global_shape, const Index &index, TDst &dest)
{
    using TOutBase = std::decay_t<TDst>;
    using TElem = typename TOutBase::element_type;
    auto gtensor = ntt::make_tensor_view<TElem>((TElem *)global_hardware_ctx.global_var, global_shape);
    ntt::tensor_copy(gtensor.view(index, dest.shape()), dest);
    arrive_and_wait(tar::@(GetName(Enumerable.Range(0, hierarchy.Length)))(@(string.Join(",", Enumerable.Repeat("0", hierarchy.Length)))), @(worldSize));
}

template <ntt::Shape GlobalShape, ntt::Shape Index, ntt::Tensor TSrc>
void tensor_boxing_store_sync(const GlobalShape &global_shape, const Index &index, TSrc &src)
{
    using TSrcBase = std::decay_t<TSrc>;
    using TElem = typename TSrcBase::element_type;
    auto gtensor = ntt::make_tensor_view<TElem>((TElem *)global_hardware_ctx.global_var, global_shape);
    ntt::tensor_copy(src, gtensor.view(index, src.shape()));
    arrive_and_wait(tar::@(GetName(Enumerable.Range(0, hierarchy.Length)))(@(string.Join(",", Enumerable.Repeat("0", hierarchy.Length)))), @(worldSize));
}

namespace detail {
template <tar::reduce_kind Kind> class group_hierarchy_getter;

@foreach(var comb in combinations) {
@:template <> class group_hierarchy_getter<tar::reduce_kind::@(GetName(comb, string.Empty))> {
  var shape = Enumerable.Range(0, hierarchy.Length).Select(i => comb.Contains(i) ?  hierarchy[i] : 1).ToArray();
  var targetValue = shape.Aggregate(1, (a, b) => a * b);
@:public:
@:    static constexpr auto group_hierarchy = ntt::fixed_shape_v<@(string.Join(", ", shape))>;
@:    static constexpr int32_t GroupTargetValue = @(targetValue);
@:};
}

template <ntt::reduce_op Op, tar::reduce_kind Kind, bool in_dm>
class tensor_reduce_sync_impl {
  public:
    void reduce_group_sync(const thread_context &ctx, int32_t target_value) const noexcept {
        @foreach(var comb in combinations) {
          var reduce_group_index = string.Join(", ", Enumerable.Range(0, hierarchy.Length).Select(i => comb.Contains(i) ? "0" : "ctx." + hierarchyNames[i] + "id()"));
        @:if constexpr (Kind == tar::reduce_kind::@(GetName(comb, string.Empty))) {
            @if(CSourceUtilities.GetRuntimeMode() != RuntimeMode.SystemMode || GetName(comb, string.Empty).Contains("rc")) {
        @:    arrive_and_wait(tar::@(GetName(comb))(@(reduce_group_index)), target_value);
            }
            else {
        @:    sync_@(GetName(comb, string.Empty))();
            }
        @:} 
        @:else
        }
        {
            static_assert(Kind == -1, "not support this Kind!");
        }
    }

    template <ntt::Shape TIndexInGroup, ntt::Shape TIndexInGlobal>
    constexpr auto index_group2global(const TIndexInGroup &index_in_group, const TIndexInGlobal &index_in_global) const noexcept {
        return ntt::generate_shape<TIndexInGlobal::rank()>([&](auto axis) {
            if constexpr (Kind & (1 << (TIndexInGlobal::rank() - axis))) {
                return index_in_group[axis];
            } else {
                return index_in_global[axis];
            }
        });
    }

    template <ntt::Shape TIndexInGlobal>
    constexpr auto index_global2group(const TIndexInGlobal &index_in_global) const noexcept {
        return ntt::generate_shape<TIndexInGlobal::rank()>([&](auto axis) {
            if constexpr (Kind & (1 << (TIndexInGlobal::rank() - axis))) {
                return index_in_global[axis];
            } else {
                return dim_zero;
            }
        });
    }

    static constexpr auto get_group_size() {
        size_t group_size = 1;
        for (size_t i = 1; i <= tar::Hierarchy.size(); i++) {
            if (Kind & (1 << i)) {
                group_size *= tar::Hierarchy[tar::Hierarchy.size() - i];
            }
        }
        return group_size;
    }

    @{
      var cur_index = string.Join(", ", Enumerable.Range(0, hierarchy.Length).Select(i => "ctx." + hierarchyNames[i] + "id()"));
    }

    template <size_t Rank>
    struct slice_with_offset
    {
        ntt::dynamic_shape_t<Rank> offset;
        ntt::dynamic_shape_t<Rank> shape;
    };

    template <size_t Rank, ntt::Shape TShape>
    ntt::dynamic_shape_t<Rank> calculate_coordinate(size_t index, const TShape &shape) {
        ntt::dynamic_shape_t<Rank> coordinate;
        for (size_t i = Rank; i > 0; --i) {
            coordinate[i-1] = index % shape[i-1];
            index /= shape[i-1];
        }
        return coordinate;
    }
    
    template <size_t Rank, class Shape>
    constexpr slice_with_offset<Rank> to_slice_offset(Shape global_shape, size_t num_devices, size_t device_idx)
    {
        slice_with_offset<Rank> result;
        auto elements_per_device = global_shape.length() / num_devices;
        auto remainder = global_shape.length() % num_devices;

        size_t current_element_index = 0UL;
        for (size_t i = 0; i < num_devices; ++i) {
            auto num_elements_on_device = elements_per_device + (i < remainder ? 1U : 0U);

            if (num_elements_on_device == 0) {
                for (size_t j = 0; j < Rank; ++j) {
                    result.shape[j] = 0;
                    result.offset[j] = 0;
                }
                break;
            }

            if (i == device_idx) {
                result.offset = calculate_coordinate<Rank>(current_element_index, global_shape);
                auto end_coordinate = calculate_coordinate<Rank>(current_element_index + num_elements_on_device - 1, global_shape);

                for (size_t dim = 0; dim < Rank; ++dim) {
                    result.shape[dim] = end_coordinate[dim] - result.offset[dim] + 1;
                }

                break;
            }
        
            current_element_index += num_elements_on_device;
        }

        return result;
    }

    template <class TSliceIn, class TSliceOut>
    void reduce_impl(TSliceIn &local, TSliceIn &remote, TSliceOut &dest) {
        if constexpr (Op == ntt::reduce_op::max) {
            ntt::binary<ntt::ops::max>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::sum ||
                             Op == ntt::reduce_op::mean) {
            ntt::binary<ntt::ops::add>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::min) {
            ntt::binary<ntt::ops::min>(local, remote, dest);
        } else if constexpr (Op == ntt::reduce_op::prod) {
            ntt::binary<ntt::ops::mul>(local, remote, dest);
        }
    }

    template <class TIn, class TOut> void operator()(TIn &src, TOut &&dest, const thread_context &ctx) {
        // collect all tensors pointer for access tensor from other nodes.
        using TElem = typename TIn::element_type;
        using TOutBase = std::decay_t<TOut>;
        constexpr size_t Rank = TIn::rank();
        constexpr auto group_hierarchy = group_hierarchy_getter<Kind>::group_hierarchy;
        auto group_target_value = group_hierarchy_getter<Kind>::GroupTargetValue;
        auto cur_index = ntt::make_shape(@(cur_index));
        auto cur_index_g = index_global2group(cur_index);
        tar::src_ptr_tensor(cur_index) =
            reinterpret_cast<void *>(src.elements().data());
        tar::dest_ptr_tensor(cur_index) =
            reinterpret_cast<void *>(dest.elements().data());
        reduce_group_sync(ctx, group_target_value);

#ifdef SYS_MODE
        if constexpr (in_dm) {
            constexpr auto group_size = ntt::fixed_dim_v<get_group_size()>;

            auto node_number_g = ntt::linear_offset(cur_index_g, group_hierarchy);
            auto slice = to_slice_offset<Rank>(TIn::shape(), group_size, node_number_g);
            constexpr auto a_shape = typename TElem::shape_type {};
            constexpr auto a_strides = typename TElem::strides_type {};
            constexpr auto rd_type = to_tdma_reduce_type<typename TElem::element_type>();
            constexpr auto tdma_type = to_tdma_type<typename TElem::element_type>();
            if (slice.shape.length() != 0)
            {
                auto viewed_src1_tensor = src.view(slice.offset, slice.shape);
                auto src_shape = viewed_src1_tensor.shape();

                for (auto i = 0; i < group_size - 1; i++)
                {
                    auto next_index_g = ntt::unravel_index((node_number_g + i + 1) % group_size, group_hierarchy);
                    auto next_index = index_group2global(next_index_g, cur_index);

                    auto src2_tensor = ntt::make_tensor_view_from_address(
                        (TElem *)tar::src_ptr_tensor(next_index),
                        src.shape(),
                        src.strides());
                    auto viewed_src2_tensor = src2_tensor.view(slice.offset, slice.shape);

                    // TODO: optimize inst numbers.
                    // apply(src_shape, [&](auto index)
                    // {
                    //     tdma_dm2dm_async<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ncdwhcx, rd_type>(a_shape, a_strides, reinterpret_cast<char *>(&viewed_src2_tensor(index)), reinterpret_cast<char *>(&viewed_src1_tensor(index)));
                    // });
                    auto full_shape = ntt::make_shape(src_shape.length() * a_shape[0], a_shape[1]);
                    tdma_dm2dm_async<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ncdwhcx, rd_type>(full_shape, a_strides, reinterpret_cast<char *>(viewed_src2_tensor.elements().data()), reinterpret_cast<char *>(viewed_src1_tensor.elements().data()));
                }
            }

            tdma_wait();
            reduce_group_sync(ctx, group_target_value);

            for (auto i = 0; i < group_size - 1; i++)
            {
                auto next_number_g = (node_number_g + i + 1) % group_size;
                auto next_slice = to_slice_offset<Rank>(TIn::shape(), group_size, next_number_g);

                if (next_slice.shape.length() != 0)
                {
                    auto next_index_g = ntt::unravel_index(next_number_g, group_hierarchy);
                    auto next_index = index_group2global(next_index_g, cur_index);

                    auto src2_tensor = ntt::make_tensor_view_from_address(
                        (TElem *)tar::src_ptr_tensor(next_index),
                        src.shape(),
                        src.strides());
                    auto viewed_src1_tensor = src.view(next_slice.offset, next_slice.shape);
                    auto viewed_src2_tensor = src2_tensor.view(next_slice.offset, next_slice.shape);

                    // TODO: optimize inst numbers.
                    // apply(viewed_src1_tensor.shape(), [&](auto index)
                    //     { tdma_dm2dm_async<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ncdwhcx, tdma_type>(a_shape, a_strides, reinterpret_cast<char *>(&viewed_src2_tensor(index)), reinterpret_cast<char *>(&viewed_src1_tensor(index))); });
                    auto full_shape = ntt::make_shape(viewed_src1_tensor.shape().length() * a_shape[0], a_shape[1]);
                    tdma_dm2dm_async<tdma_mode::tile, mem_format::ncdwhcx, mem_format::ncdwhcx, tdma_type>(full_shape, a_strides, reinterpret_cast<char *>(viewed_src2_tensor.elements().data()), reinterpret_cast<char *>(viewed_src1_tensor.elements().data()));
                }
            }

            tdma_wait();
            reduce_group_sync(ctx, group_target_value);
        }
        else
#endif
        {
            // according to the group size split the tensor.
            // todo should using better split strategy.
            constexpr auto group_size = ntt::fixed_dim_v<get_group_size()>;
            // FIXME: support axis calculation.
            constexpr auto axis = ntt::fixed_dim_v<0>;
            // constexpr auto axis = ntt::fixed_dim_v<[&] {
            //     dim_t axis = -1;
            //     loop<Rank>([&](auto i) {
            //         if (axis == -1 && src.shape()[i] >= group_size) {
            //             axis = i;
            //         }
            //     });
            //     if (axis == -1) {
            //         axis = 0;
            //     }
            //     return axis;
            // }()>;

            auto remain = src.shape()[axis] % (group_size);
            auto frac = src.shape()[axis] / (group_size);

            auto node_number_g = ntt::linear_offset(cur_index_g, group_hierarchy);
            auto next_index_g = ntt::unravel_index((node_number_g + 1) % group_size, group_hierarchy);

            // keep the non-reduce axis invariant.
            auto next_index = index_group2global(next_index_g, cur_index);

            // reduce-scatter
            for (auto i = 0; i < group_size - 1; i++) // communicate (group_size - 1) times
            {
                // check when the last time.
                auto offset = (node_number_g + i + 2) % group_size;
                auto new_shape = src.shape().template replace_at<axis>(ntt::where(offset == group_size - 1, frac + remain, frac));
                auto starts = ntt::generate_shape<Rank>([&](auto j) {
                    if constexpr (j == axis) {
                        return offset * frac;
                    } else {
                        return dim_zero;
                    }
                });
                auto viewed_src1_tensor = src.view(starts, new_shape);
                auto viewed_dest_tensor = dest.view(starts, new_shape);

                if (i == 0) {
                    auto src2_tensor = ntt::make_tensor_view_from_address(
                        (TElem *)tar::src_ptr_tensor(next_index),
                        src.shape(),
                        src.strides());
                    auto viewed_src2_tensor = src2_tensor.view(starts, new_shape);
                    reduce_impl(viewed_src1_tensor, viewed_src2_tensor,
                                viewed_dest_tensor);
                } else {
                    auto src2_tensor = ntt::make_tensor_view_from_address(
                        (TElem *)tar::dest_ptr_tensor(next_index),
                        dest.shape(),
                        dest.strides());
                    auto viewed_src2_tensor = src2_tensor.view(starts, new_shape);
                    reduce_impl(viewed_src1_tensor, viewed_src2_tensor,
                                viewed_dest_tensor);
                }
                
                reduce_group_sync(ctx, group_target_value);
            }
            
            // all gather
            auto dest_index_g = ntt::unravel_index((node_number_g + group_size - 1) % group_size, group_hierarchy);
            auto dest_index = index_group2global(dest_index_g, cur_index);

            auto dest_tensor = ntt::make_tensor_view_from_address(
                (TElem *)tar::dest_ptr_tensor(dest_index),
                dest.shape(),
                dest.strides());
            for (size_t i = 0; i < group_size - 1; i++) {
                auto offset = (node_number_g + i) % (group_size);
                auto starts = ntt::generate_shape<Rank>([&](auto j) {
                    if constexpr (j == axis) {
                        return offset * frac;
                    } else {
                        return dim_zero;
                    }
                });

                auto new_shape = src.shape().template replace_at<axis>(ntt::where(offset == group_size - 1, frac + remain, frac));
                auto viewed_src_tensor = dest.view(starts, new_shape);
                auto viewed_dest_tensor = dest_tensor.view(starts, new_shape);
                ntt::tensor_copy(viewed_src_tensor,
                                 viewed_dest_tensor);

                reduce_group_sync(ctx, group_target_value);
            }
        }
    }
};
} // namespace detail

template <ntt::reduce_op Op, tar::reduce_kind Kind, bool in_dm = false, class TIn,
          class TOut>
void tensor_reduce_sync(TIn &input, TOut &&output, const thread_context &ctx){
  detail::tensor_reduce_sync_impl<Op, Kind, in_dm> impl;
  impl(input, output, ctx);
}
}
