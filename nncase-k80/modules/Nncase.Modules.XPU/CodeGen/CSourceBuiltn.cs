﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
#define MULTI_CORE_XPU
using System.Runtime.CompilerServices;
using DryIoc.ImTools;
using NetFabric.Hyperlinq;
using Nncase.CodeGen.NTT;
using Nncase.IR;
using Nncase.Targets;
using Razor.Templating.Core;

namespace Nncase.CodeGen.XPU;

public static class CSourceBuiltn
{
#if MULTI_CORE_XPU
    public static string ClusterDef()
    {
        return @"#pragma once
#include <hardware_context.h>
#include <tdma.h>
#include ""kernel.h""
";
    }

    public static string TopoAwareRuntimeDef(NTTTargetOptions options, ulong dataAlign, ulong collective_pool_size)
    {
        var content = RazorTemplateEngine.RenderAsync("~/CodeGen/Templates/topo_aware_runtime.cshtml", new NTTTargetOptionsModel(options, dataAlign, collective_pool_size)).Result;
        return content;
    }

    public static string CMakeDef(string name, [CallerFilePath] string? callerFilePath = null)
    {
        var sysMod = CSourceUtilities.GetRuntimeMode();

        var mainSignature = sysMod == RuntimeMode.SystemMode ? "-static" : @"-Wl,-e,_Z6_startPcPN6nncase7runtime3xpu19hardware_context_mtEPNS2_15runtime_util_mtEPNS2_19nncase_method_tableERSt6vectorINS0_3ntt7runtime17thread_inout_descESaISC_EESF_PhPmSG_SH_SG_SG_";

        var runtimePath = Path.Combine(Path.GetDirectoryName(typeof(CSourceBuiltn).Assembly.Location)!, "Runtime");

        var sdkInc = sysMod == RuntimeMode.SystemMode ? $@"if (DUCA_XPUSDK_PATH)
add_definitions(-DSYS_MODE)
include_directories($ENV{{DUCA_XPUSDK_PATH}}/include/ ${{CMAKE_CURRENT_SOURCE_DIR}} {runtimePath}/include/nncase/runtime/xpu/kernel/)
endif()
if (DUMP_INST)
add_definitions(-DDUMP_INST)
endif()" : string.Empty;

        var sdkLd = sysMod == RuntimeMode.SystemMode ? @$"if (DUCA_XPUSDK_PATH)
target_link_libraries({name} nncase_ntt_module $ENV{{DUCA_XPUSDK_PATH}}/lib/libxpusdk.a)
endif()" : string.Empty;

        var compileOptions = sysMod == RuntimeMode.SystemMode ? @"add_compile_options(
    --target=riscv64-unknown-elf
    --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH}
    --sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf
    -Wall
    -fomit-frame-pointer
    -fno-strict-aliasing
    -fno-builtin
    -mtls-dialect=desc
    -ffunction-sections
    -menable-experimental-extensions
    -fdata-sections
    -DCFG_MAKEFILE
    -fno-pic
    -fno-PIC
    -fexceptions
    # -march=rv64gcv_zvl1024b_zvfh_xtheadvsfa_xducakernel
    # -march=rv64gcv_zvl1024b_zvfh_xtheadvsfa_xducakernel_xducacopif_xtheadsync_xtheadlpw
    # -mcpu=c908x
    -fdeclspec
    -mrvv-vector-bits=zvl
    -Wno-narrowing
)

set(CMAKE_LINKER ld.lld)
set(LINKER ""$ENV{DUCA_XPUSDK_PATH}/link.lds"")
# Set the linker flags
set(CMAKE_LIB_LINKER_FLAGS ""-fuse-ld=lld --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH} --sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf --target=riscv64-unknown-elf -static -Wl,--gc-sections -Wl,--library=c -Wl,--library=gloss -Wl,--library=g -Wl,--no-relax -Wl,--wrap=printf -Wl,--wrap=exit -Wl,--wrap=malloc -T ${LINKER}"")
set(CMAKE_EXE_LINKER_FLAGS ""-fuse-ld=lld --gcc-toolchain=$ENV{DUCA_TOOLCHAIN_PATH} --sysroot=$ENV{DUCA_TOOLCHAIN_PATH}/riscv64-unknown-elf --target=riscv64-unknown-elf -static -Wl,--gc-sections -Wl,--library=c -Wl,--library=gloss -Wl,--library=g -Wl,--no-relax -Wl,--wrap=printf -Wl,--wrap=exit -Wl,--wrap=malloc -T ${LINKER}"")"
: @"add_compile_options(-fPIC -fexceptions)";

        var target = sysMod == RuntimeMode.SystemMode ? @$"add_executable({name} ""main.cpp"")
target_compile_options({name} PRIVATE -mcmodel=medany)
target_include_directories(
  {name} PUBLIC {runtimePath}/include/nncase/runtime/xpu/kernel/ {runtimePath}/include/
)" : @$"add_library({name} SHARED ""main.cpp"")
target_include_directories(
  {name} PUBLIC {runtimePath}/include/nncase/runtime/xpu/kernel/ {runtimePath}/include/
)
target_compile_options({name} PRIVATE -fvisibility=default)
target_link_libraries({name} PUBLIC nncase_ntt_module)
set_target_properties({name} PROPERTIES POSITION_INDEPENDENT_CODE ON)
set_target_properties({name} PROPERTIES PREFIX """" SUFFIX """")";

        return $@"cmake_minimum_required(VERSION 3.13)
project(xpu)
# remove for newlib
string(REGEX REPLACE ""-latomic"" """" CMAKE_CXX_STANDARD_LIBRARIES ""${{CMAKE_CXX_STANDARD_LIBRARIES}}"")
{sdkInc}

add_compile_options(-O3)

include({Path.Combine(runtimePath, "src", "xpu_runtime.cmake")})

{compileOptions}

set(CMAKE_CXX_STANDARD 20)

if (DISABLE_RVV)
  add_definitions(-DDISABLE_RVV)
endif()

add_link_options(
  -no-pie
  #-fPIE
  -fno-stack-protector
  {mainSignature}
)

{target}
{sdkLd}
";
    }

    public static string[] MakeKernel(string ctype, string kernelImpl, NTTTargetOptions targetOptions)
    {
        var topo_thread_context = @"
thread_local xpu_thread_context_t xpu_thread_context;
xpu_thread_context_t &xpu_thread_context_t::current() noexcept {{
    return xpu_thread_context;
}}";

        string paged_attn_header_path = string.Empty;
        if (CSourceUtilities.GetRuntimeMode() is RuntimeMode.SystemMode && !string.IsNullOrEmpty(targetOptions.CustomOpScheme))
        {
            using (var fs = File.OpenRead(targetOptions.CustomOpScheme))
            {
                var opscheme = System.Text.Json.JsonSerializer.Deserialize<Passes.Distributed.CustomOpScheme>(fs);
                var node = opscheme!.Outputs.Where(node => node.Op == "PagedAttention").Single();
                paged_attn_header_path = $"#include <{node.CSourcePath}>";
            }
        }

        return new[] {
            @$"#include ""thread_context.h""
#include <runtime_utils.h>
#include <hardware_context.h>
// #include <tdma.h>
#ifdef SYS_MODE
extern ""C"" {{
#include <nano_kernel_lib.h>
}}

__shared_point__ unsigned long       vhm_va_start;          /*nano kernel可以直接访问,虚拟地址*/
// extern __shared_point__ unsigned long       vhm_va_size;

__shared_point__ unsigned long       global_vhm_va_start;          /*nano kernel可以直接访问,虚拟地址*/
__shared_point__ unsigned long       global_vhm_va_size;

__shared_point__ unsigned long       sram_va_start;         /*nano kernel可以直接访问,虚拟地址*/
__shared_point__ unsigned long       sram_va_size;

__shared_point__ unsigned long       global_sram_va_start;          /*nano kernel可以直接访问,虚拟地址*/
__shared_point__ unsigned long       global_sram_va_size;

#include ""copy.h""
#include ""ukernels/u_unary.h""
#include ""ukernels/u_matmul.h""
#include ""riscv_duca_builtin_vars.h""
#include <nncase/float8.h>
__device__ decltype(nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_local_data_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);

__device__ decltype(nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_thread_local_rdata_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);

__device__ decltype(nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_block_local_rdata_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);
#endif
#include ""topo_aware_runtime.h""
#include <nncase/ntt/ntt.h>
#include <nncase/ntt/kernels/paged_attention.h>
#ifdef SYS_MODE
{paged_attn_header_path}
#endif
using namespace nncase;
using namespace nncase::ntt;
using namespace nncase::ntt::distributed;
using namespace nncase::ntt::distributed::shard_policy;
using namespace nncase::ntt::runtime;
using namespace nncase::runtime;

" + topo_thread_context + "\n",
            ctype + "{\n" + kernelImpl + "\n}\n",
            };
    }

    public static string[] MakeDeviceFuncHeader(string[] deviceFuncs)
    {
        return deviceFuncs.Distinct().Select(f => $"#include \"../device/{f}.h\"").ToArray();
    }

    public static string[] MakeMain(TIR.PrimFunction primFunction, ulong dataAlign, ulong dataUsage, ulong rdataPoolSize, ulong threadLocalRdataPoolSize, ulong blockLocalRdataPoolSize, NTTTargetOptions options)
    {
        var kernel = RazorTemplateEngine.RenderAsync("~/CodeGen/Templates/kernel.cpp.cshtml", new KernelMainModel(primFunction, options, dataAlign, dataUsage, rdataPoolSize, threadLocalRdataPoolSize, blockLocalRdataPoolSize)).Result;

        if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
        {
            return new[] {
@$"#include ""kernel.h""" + "\n\n",
kernel,
@$"int main(void){{

    printf(""Finished main!\r\n"");
	exit(0);
}}
", };
        }

        return new[] { @$"#if defined(__riscv) && defined(__riscv_vector) && defined(DISABLE_RVV)
#undef __riscv_vector
#endif
#include ""kernel.h""
#include <runtime_utils.h>
#include <thread>
" + "\n\n",
kernel,
@$"
PREFIX decltype(nncase::ntt::make_tensor<
                       nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_local_data_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);

PREFIX decltype(nncase::ntt::make_tensor<
                       nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_thread_local_rdata_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);

PREFIX decltype(nncase::ntt::make_tensor<
                       nncase::ntt::vector<uintptr_t, 2>>(
    nncase::ntt::distributed::topology_shape))
    nncase::ntt::distributed::detail::global_block_local_rdata_ptr =
        nncase::ntt::make_tensor<nncase::ntt::vector<uintptr_t, 2>>(
            nncase::ntt::distributed::topology_shape);

int main() {{}}

// NOTE do not modify the _start's signature, we use the mangled name to find this entry.
void _start(char* name, hardware_context_mt *hw_ctx_impl, runtime_util_mt *rt_util_mt,
            nncase_mt_t *nncase_mt_impl, std::vector<thread_inout_desc> &input_descs, std::vector<thread_inout_desc> &output_descs,
            uint8_t *rdata, uint64_t *thread_local_rdata_header, uint8_t *thread_local_rdata_content, uint64_t *block_local_rdata_header, uint8_t *block_local_rdata_content, uint8_t *output_data) {{
    runtime_util = rt_util_mt;
    nncase_mt = nncase_mt_impl;
    for (int b=0; b<NANOS; b++)
    {{
        sram[b] = (uint8_t *)runtime_util->malloc(sram_size_per_block, {dataAlign});
        vhm[b] = (uint8_t *)runtime_util->malloc(vhm_size_per_block, {dataAlign});
    }}

    std::vector<std::thread> threads;
" + "\n\n",

@$"    if (strcmp(name, ""{primFunction.Name}_wrapper"") == 0)
    {{
        for (unsigned long t=0; t<NANOS*THREADS; t++)
        {{
            auto thread_local_rdata = thread_local_rdata_content + thread_local_rdata_header[t * 2];
            auto block_local_rdata = block_local_rdata_content + block_local_rdata_header[t / THREADS * 2];
            threads.emplace_back([t, input_descs, &output_descs, rdata, thread_local_rdata, block_local_rdata, output_data]{{ {primFunction.Name}_wrapper(t, input_descs.data(), output_descs.data(), rdata, thread_local_rdata, block_local_rdata, output_data);}});
        }}
    }}" + "\n\n",

@$"    for (auto &t : threads)
        t.join();

    for (int b=0; b<NANOS; b++)
    {{
        runtime_util->free(sram[b]);
        runtime_util->free(vhm[b]);
    }}
}}", };
    }

    public static string MakeShared(string shareds)
    {
        return @$"#include <tdma.h>

namespace shared {{
{shareds}
}} // namespace shared";
    }

#else
    public const string BufferType = "buffer_t";

    public const string BufferStruct = @"typedef struct buffer {
    void *vaddr;
    size_t paddr;
    uint32_t *shape;
    uint32_t *stride;
    uint32_t rank;
} buffer_t;";

    public const string MethodTable = @"typedef struct nncase_method_table {
    // float unary
    float (*float_unary_abs)(float);
    float (*float_unary_acos)(float);
    float (*float_unary_acosh)(float);
    float (*float_unary_asin)(float);
    float (*float_unary_asinh)(float);
    float (*float_unary_ceil)(float);
    float (*float_unary_cos)(float);
    float (*float_unary_cosh)(float);
    float (*float_unary_exp)(float);
    float (*float_unary_floor)(float);
    float (*float_unary_log)(float);
    float (*float_unary_logical_not)(float);
    float (*float_unary_neg)(float);
    float (*float_unary_round)(float);
    float (*float_unary_rsqrt)(float);
    float (*float_unary_sign)(float);
    float (*float_unary_sin)(float);
    float (*float_unary_sinh)(float);
    float (*float_unary_sqrt)(float);
    float (*float_unary_square)(float);
    float (*float_unary_tanh)(float);
    // float bianry
    float (*float_binary_add)(float, float);
    float (*float_binary_sub)(float, float);
    float (*float_binary_mul)(float, float);
    float (*float_binary_div)(float, float);
    float (*float_binary_min)(float, float);
    float (*float_binary_max)(float, float);
    float (*float_binary_pow)(float, float);
    float (*float_binary_logical_and)(float, float);
    float (*float_binary_mod)(float, float);
    // int32 bianry
    int32_t (*int32_binary_add)(int32_t, int32_t);
    int32_t (*int32_binary_sub)(int32_t, int32_t);
    int32_t (*int32_binary_mul)(int32_t, int32_t);
    int32_t (*int32_binary_div)(int32_t, int32_t);
    int32_t (*int32_binary_min)(int32_t, int32_t);
    int32_t (*int32_binary_max)(int32_t, int32_t);
    int32_t (*int32_binary_pow)(int32_t, int32_t);
    int32_t (*int32_binary_logical_and)(int32_t, int32_t);
    int32_t (*int32_binary_mod)(int32_t, int32_t);
    // int64 bianry
    int64_t (*int64_binary_add)(int64_t, int64_t);
    int64_t (*int64_binary_sub)(int64_t, int64_t);
    int64_t (*int64_binary_mul)(int64_t, int64_t);
    int64_t (*int64_binary_div)(int64_t, int64_t);
    int64_t (*int64_binary_min)(int64_t, int64_t);
    int64_t (*int64_binary_max)(int64_t, int64_t);
    int64_t (*int64_binary_pow)(int64_t, int64_t);
    int64_t (*int64_binary_logical_and)(int64_t, int64_t);
    int64_t (*int64_binary_mod)(int64_t, int64_t);
    // bool binary
    bool (*bool_binary_and)(bool, bool);
    bool (*bool_binary_or)(bool, bool);
    bool (*bool_binary_xor)(bool, bool);
} nncase_mt_t;";

    public const string Include = @"#include <stdbool.h>\n#include <stdint.h>\n#include <stddef.h>";

    public const string FixedParameters = "nncase_mt_t* nncase_mt, uint8_t* data, const uint8_t* rdata";

    public const string MainPrologue = $@"void _start(size_t func_id, uint8_t** buffers, {FixedParameters}) {{";

    public const string MainEpilogue = @"}";

    public static string Header => $@"
{Include}

{MethodTable}

{BufferStruct}
";
#endif
}
