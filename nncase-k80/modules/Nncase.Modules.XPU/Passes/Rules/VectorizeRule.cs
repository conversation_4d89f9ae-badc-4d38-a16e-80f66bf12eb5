﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DryIoc.ImTools;
using Nncase.CodeGen.XPU;
using Nncase.IR;
using Nncase.IR.Shapes;
using Nncase.Passes.Rules.NTT;
using Nncase.PatternMatch;
using Nncase.PatternMatch.F;
using Nncase.Targets;
using Nncase.Utilities;

using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Imaging;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.F.NN;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.XPU;

public sealed class VectorizeMatMul : VectorizeRule
{
    public VectorizeMatMul(int rank = 2, int lane = 128, bool transB = false)
        : base(rank, lane)
    {
        TransB = transB;
    }

    public override Pattern Pattern { get; } = IsMatMul(
      "matmul",
      "target",
      (dytpe) => true,
      IsWildcard("lhs", e => e is not Call { Target: IR.Tensors.Unpack }) with { TypePattern = IsFloat() & !IsVector() },
      IsWildcard("rhs", e => e is not Call { Target: IR.Tensors.Unpack }) with { TypePattern = IsFloat() & !IsVector() });

    /// <summary>
    /// Gets a value indicating whether trans b, note only for test.
    /// </summary>
    public bool TransB { get; }

    public override List<Expr> GetReplaceCandidates(IMatchResult result, RunPassContext context)
    {
        var rets = new List<Expr>();
        var lhs = (Expr)result["lhs"];
        var rhs = (Expr)result["rhs"];
        var candidate = (Expr)result[Pattern];
        var lhsShape = lhs.CheckedShape;
        var rhsShape = rhs.CheckedShape;
        var outputDataType = ((Nncase.IR.Math.MatMul)result["matmul"]).OutputDataType;
        var rcontext = new RuleContext(rets, lhs, rhs, candidate, lhsShape, rhsShape, outputDataType);

        // TODO: remove this when numa is enough.
        if (lhs.CheckedDataType == DataTypes.Float32 &&
            rhs is TensorConst tc &&
            TensorUtilities.GetSize(tc.CheckedShape.ToValueArray(), TensorUtilities.GetDefaultStrides(tc.CheckedShape.ToValueArray()), DataTypes.Float32.SizeInBytes) > 544538620 &&
            tc.CheckedShape.ToValueArray()[^1] % (64 * 32) != 0)
        {
            return null!;
        }

        if (lhs.CheckedDataType == DataTypes.Float16 && rhs is TensorConst tc_f16)
        {
            if (TensorUtilities.GetSize(tc_f16.CheckedShape.ToValueArray(), TensorUtilities.GetDefaultStrides(tc_f16.CheckedShape.ToValueArray()), DataTypes.Float16.SizeInBytes) > 2L * 1000 * 1000 * 100 &&
                tc_f16.CheckedShape.ToValueArray()[^1] % (64 * 32) != 0)
            {
                return null!;
            }
        }

        // pack A's k and B's k
        // AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.K, IR.NTT.VectorizedMatMul.VectorizeKind.K);

        // only pack A's m
        // AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.M, IR.NTT.VectorizedMatMul.VectorizeKind.None);

        // only pack B's n
        // AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.None, IR.NTT.VectorizedMatMul.VectorizeKind.N/* , transB: rhs is Const */);
        if (Rank > 1)
        {
            // pack A's m and B's n, when B is const, force transpose
            // AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.M, IR.NTT.VectorizedMatMul.VectorizeKind.N/* , transB: rhs is Const */);

            // pack A's m,k and B's k,n
            AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.K, IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N/* , transB: rhs is Const */);
            if (TransB)
            {
                AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.M | IR.NTT.VectorizedMatMul.VectorizeKind.K, IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N, transB: TransB);
            }

            // pack A's m,k and B's k
            // AddCandidate(rcontext,  IR.NTT.VectorizedMatMul.VectorizeKind.M |  IR.NTT.VectorizedMatMul.VectorizeKind.K,  IR.NTT.VectorizedMatMul.VectorizeKind.K);

            // pack A's k and B's k,n
            // AddCandidate(rcontext, IR.NTT.VectorizedMatMul.VectorizeKind.K, IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N/* , transB: lhs is Const */);
        }

        return rets;
    }

    private void AddCandidate(RuleContext context, IR.NTT.VectorizedMatMul.VectorizeKind lhsPack, IR.NTT.VectorizedMatMul.VectorizeKind rhsPack, bool transA = false, bool transB = false)
    {
        var (rets, lhs, rhs, candidate, _, _, outputDataType) = context;
        var lhsShape = context.LhsShape.ToArray();
        var rhsShape = context.RhsShape.ToArray();
        var lhsLaneSize = Lane / lhs.CheckedDataType.SizeInBytes;
        var rhsLaneSize = Lane / rhs.CheckedDataType.SizeInBytes;
        if (transA)
        {
            var perm = Enumerable.Range(0, lhsShape.Length).ToArray();
            (perm[^2], perm[^1]) = (perm[^1], perm[^2]);
            (lhsShape[^2], lhsShape[^1]) = (lhsShape[^1], lhsShape[^2]);
            lhs = IR.F.Tensors.Transpose(lhs, perm);
        }

        if (transB)
        {
            var perm = Enumerable.Range(0, rhsShape.Length).ToArray();
            (perm[^2], perm[^1]) = (perm[^1], perm[^2]);
            (rhsShape[^2], rhsShape[^1]) = (rhsShape[^1], rhsShape[^2]);
            rhs = IR.F.Tensors.Transpose(rhs, perm);
        }

        int[] lhsLanes;
        int[] lhsPackedAxes;
        var (lm, lk) = transA ? (lhsShape.Length - 1, lhsShape.Length - 2) : (lhsShape.Length - 2, lhsShape.Length - 1);
        var (rk, rn) = transB ? (rhsShape.Length - 1, rhsShape.Length - 2) : (rhsShape.Length - 2, rhsShape.Length - 1);
        switch (lhsPack)
        {
            case IR.NTT.VectorizedMatMul.VectorizeKind.None:
                lhsLanes = Array.Empty<int>();
                lhsPackedAxes = Array.Empty<int>();
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.M:
                lhsLanes = [lhsLaneSize];
                lhsPackedAxes = [lm];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K:
                lhsLanes = [lhsLaneSize];
                lhsPackedAxes = [lk];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.M | IR.NTT.VectorizedMatMul.VectorizeKind.K:
                lhsLanes = [64, lhsLaneSize];
                lhsPackedAxes = [lm, lk];
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(lhsPack), lhsPack.ToString());
        }

        int[] rhsLanes;
        int[] rhsPackedAxes;
        switch (rhsPack)
        {
            case IR.NTT.VectorizedMatMul.VectorizeKind.None:
                rhsLanes = Array.Empty<int>();
                rhsPackedAxes = Array.Empty<int>();
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.N:
                rhsLanes = [rhsLaneSize];
                rhsPackedAxes = [rn];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K:
                rhsLanes = [rhsLaneSize];
                rhsPackedAxes = [rk];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N:
                if (rhs.CheckedDataType.SizeInBytes == 1 || rhs.CheckedDataType.SizeInBytes == 4)
                {
                    // TODO: remove this when tiling is ready.
#if false
                    if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
                    {
                        rhsLanes = [64, rhsLaneSize];
                        rhsPackedAxes = [rn, rk];
                    }
                    else
#endif
                    {
                        rhsLanes = [rhsLaneSize, 64];
                        rhsPackedAxes = [rk, rn];
                    }
                }
                else
                {
                    rhsLanes = [rhsLaneSize, 64];
                    rhsPackedAxes = [rk, rn];
                }

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(rhsPack), rhsPack.ToString());
        }

        var extraPadsB = Enumerable.Repeat(0, rhsShape.Length).ToArray();
#if false
        if (rhs is TensorConst)
        {
            var hierarchiesN = (CompileSession.CompileOptions.TargetOptions as NTTTargetOptions)!.Hierarchies[0][2..4];
            var (initPadN, extraPadN) = FindMinimumPad((int)rhsShape[rn].FixedValue, rhsLaneSize, hierarchiesN.Aggregate(1, (a, b) => a * b));
            extraPadsB[rn] = initPadN + extraPadN;
        }
#endif

        var padValue = Tensor.FromScalar(DataTypes.Float32, 0f).CastTo(lhs.CheckedDataType);
        var vectorizedLhs = IR.F.Tensors.Pack(VectorizeUtility.PadForVectorize(lhs, lhsShape, lhsPackedAxes, lhsLanes, padValue, out var lhsPadNums), lhsLanes, lhsPackedAxes);
        var vectorizedRhs = IR.F.Tensors.Pack(VectorizeUtility.PadForVectorize(rhs, rhsShape, rhsPackedAxes, rhsLanes, padValue, out var rhsPadNums, extraPadsB), rhsLanes, rhsPackedAxes);

        var matmul = IR.F.NTT.VectorizedMatMul(vectorizedLhs, vectorizedRhs, lhsPackedAxes, rhsPackedAxes, transA, transB, false, outputDataType);

        var outRank = System.Math.Max(lhsShape.Length, rhsShape.Length);
        var lhsAlign = outRank - lhsShape.Length;
        var rhsAlign = outRank - rhsShape.Length;

        var unpackAxes = new List<int>();
        var unpadNums = new List<Dimension>();
        var unpackLanes = new List<int>();
        if (lhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.M))
        {
            var mPackIndex = Array.IndexOf(lhsPackedAxes, lm);
            unpackAxes.Add(outRank - 2);
            unpadNums.Add(lhsPadNums[mPackIndex]);
            unpackLanes.Add(64);
        }

        if (rhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.N))
        {
            var nPackIndex = Array.IndexOf(rhsPackedAxes, rn);
            unpackAxes.Add(outRank - 1);
            unpadNums.Add(rhsPadNums[nPackIndex]);
            unpackLanes.Add(64);
        }

        Expr post = matmul;
        if (unpackAxes.Any())
        {
            post = VectorizeUtility.SliceForVectorize(IR.F.Tensors.Unpack(matmul, unpackLanes.ToArray(), unpackAxes.ToArray()), candidate.CheckedShape, unpadNums.ToArray());
        }

        if (post.CheckedType is not InvalidType)
        {
            rets.Add(post);
        }
    }

    private sealed record RuleContext(List<Expr> Results, Expr Lhs, Expr Rhs, Expr Candidate, Shape LhsShape, Shape RhsShape, DataType OutputDataType)
    {
    }
}

public sealed class VectorizeCustomMatMul : VectorizeRule
{
    public VectorizeCustomMatMul(int rank = 2, int lane = 128)
        : base(rank, lane)
    {
    }

    public override Pattern Pattern { get; } = CustomNTT.IsMatMul(
        "target",
        "call",
        _ => true,
        IsWildcard("lhs", e => e is not Call { Target: IR.Tensors.Unpack }) with { TypePattern = IsFloat() & !IsVector() },
        IsWildcard("rhs", e => e is not Call { Target: IR.Tensors.Unpack }) with { TypePattern = IsFloat() & !IsVector() });

    public override Expr? GetReplace(IMatchResult result, RunPassContext context)
    {
        var rets = new List<Expr>();
        var lhs = (Expr)result["lhs"];
        var rhs = (Expr)result["rhs"];
        var target = (IR.CustomNTT.MatMul)result["target"];
        var candidate = (Expr)result[Pattern];
        var lhsShape = lhs.CheckedShape;
        var rhsShape = rhs.CheckedShape;
        var rcontext = new RuleContext(rets, target, lhs, rhs, candidate, lhsShape, rhsShape);

        var lhsPack = IR.NTT.VectorizedMatMul.VectorizeKind.K;

        // var rhsPack = rhs is TensorConst ? IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N : IR.NTT.VectorizedMatMul.VectorizeKind.K;
        var rhsPack = IR.NTT.VectorizedMatMul.VectorizeKind.K;
        AddCandidate(rcontext, lhsPack, rhsPack, true, false);

        return rets[0];
    }

    private void AddCandidate(RuleContext context, IR.NTT.VectorizedMatMul.VectorizeKind lhsPack, IR.NTT.VectorizedMatMul.VectorizeKind rhsPack, bool transA = false, bool transB = false)
    {
        var (rets, target, lhs, rhs, candidate, _, _) = context;
        var lhsShape = context.LhsShape.ToArray();
        var rhsShape = context.RhsShape.ToArray();
        var lhsLaneSize = Lane / lhs.CheckedDataType.SizeInBytes;
        var rhsLaneSize = Lane / rhs.CheckedDataType.SizeInBytes;
        var outLaneSize = Lane / candidate.CheckedDataType.SizeInBytes;
        if (transA)
        {
            var perm = Enumerable.Range(0, lhsShape.Length).ToArray();
            (perm[^2], perm[^1]) = (perm[^1], perm[^2]);
            (lhsShape[^2], lhsShape[^1]) = (lhsShape[^1], lhsShape[^2]);
            lhs = IR.F.Tensors.Transpose(lhs, perm);
        }

        if (transB)
        {
            var perm = Enumerable.Range(0, rhsShape.Length).ToArray();
            (perm[^2], perm[^1]) = (perm[^1], perm[^2]);
            (rhsShape[^2], rhsShape[^1]) = (rhsShape[^1], rhsShape[^2]);
            rhs = IR.F.Tensors.Transpose(rhs, perm);
        }

        int[] lhsLanes;
        int[] lhsPackedAxes;
        var (lm, lk) = transA ? (lhsShape.Length - 1, lhsShape.Length - 2) : (lhsShape.Length - 2, lhsShape.Length - 1);
        var (rk, rn) = transB ? (rhsShape.Length - 1, rhsShape.Length - 2) : (rhsShape.Length - 2, rhsShape.Length - 1);
        switch (lhsPack)
        {
            case IR.NTT.VectorizedMatMul.VectorizeKind.None:
                lhsLanes = Array.Empty<int>();
                lhsPackedAxes = Array.Empty<int>();
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.M:
                lhsLanes = [lhsLaneSize];
                lhsPackedAxes = [lm];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K:
                lhsLanes = [lhsLaneSize];
                lhsPackedAxes = [lk];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.M | IR.NTT.VectorizedMatMul.VectorizeKind.K:
                lhsLanes = [64, lhsLaneSize];
                lhsPackedAxes = [lm, lk];
                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(lhsPack), lhsPack.ToString());
        }

        int[] rhsLanes;
        int[] rhsPackedAxes;
        switch (rhsPack)
        {
            case IR.NTT.VectorizedMatMul.VectorizeKind.None:
                rhsLanes = Array.Empty<int>();
                rhsPackedAxes = Array.Empty<int>();
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.N:
                rhsLanes = [rhsLaneSize];
                rhsPackedAxes = [rn];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K:
                rhsLanes = [rhsLaneSize];
                rhsPackedAxes = [rk];
                break;
            case IR.NTT.VectorizedMatMul.VectorizeKind.K | IR.NTT.VectorizedMatMul.VectorizeKind.N:
                if (rhs.CheckedDataType.SizeInBytes == 1)
                {
                    rhsLanes = [64, rhsLaneSize];
                    rhsPackedAxes = [rn, rk];
                }
                else if (rhs.CheckedDataType.SizeInBytes == 2)
                {
                    rhsLanes = [rhsLaneSize, 64];
                    rhsPackedAxes = [rk, rn];
                }
                else
                {
                    if (CSourceUtilities.GetRuntimeMode() == RuntimeMode.SystemMode)
                    {
                        rhsLanes = [64, rhsLaneSize];
                        rhsPackedAxes = [rn, rk];
                    }
                    else
                    {
                        rhsLanes = [rhsLaneSize, 64];
                        rhsPackedAxes = [rk, rn];
                    }
                }

                break;
            default:
                throw new ArgumentOutOfRangeException(nameof(rhsPack), rhsPack.ToString());
        }

        var sbpsA = target.LhsSBPs.ToArray();
        var sbpsB = target.RhsSBPs.ToArray();
        var sbpsC = target.OutSBPs.ToArray();
        if (transA)
        {
            (sbpsA[^1], sbpsA[^2]) = (sbpsA[^2], sbpsA[^1]);
        }

        if (transB)
        {
            (sbpsB[^1], sbpsB[^2]) = (sbpsB[^2], sbpsB[^1]);
        }

        if (transA || transB)
        {
            (sbpsC[^1], sbpsC[^2]) = (sbpsC[^2], sbpsC[^1]);
        }

        var extraPadsA = Enumerable.Repeat(0, lhsShape.Length).ToArray();
        var extraPadsB = Enumerable.Repeat(0, rhsShape.Length).ToArray();
        var hierarchiesM = sbpsA[lm] is SBPSplit sam ? sam.Axes.Select(a => (CompileSession.CompileOptions.TargetOptions as NTTTargetOptions)!.Hierarchies[0][a]) : new[] { 1 };
        var hierarchiesK = sbpsA[lk] is SBPSplit sa ? sa.Axes.Select(a => (CompileSession.CompileOptions.TargetOptions as NTTTargetOptions)!.Hierarchies[0][a]) : new[] { 1 };
        var hierarchiesN = sbpsB[rn] is SBPSplit sb ? sb.Axes.Select(a => (CompileSession.CompileOptions.TargetOptions as NTTTargetOptions)!.Hierarchies[0][a]) : new[] { 1 };
        extraPadsA[lk] = FindMinimumPad((int)lhsShape[lk].FixedValue, lhsLaneSize, hierarchiesK.Aggregate(1, (a, b) => a * b)).ExtraPad;
        extraPadsB[rk] = FindMinimumPad((int)rhsShape[rk].FixedValue, rhsLaneSize, hierarchiesK.Aggregate(1, (a, b) => a * b)).ExtraPad;
        var (initPadN, extraPadN) = FindMinimumPad((int)rhsShape[rn].FixedValue, rhsLaneSize, hierarchiesN.Aggregate(1, (a, b) => a * b));
        extraPadsB[rn] = initPadN + extraPadN;
        var padValue = Tensor.FromScalar(DataTypes.Float32, 0f).CastTo(lhs.CheckedDataType);
        var paddings = Paddings.Zeros(lhsShape.Length).ToDimensionArray();
        paddings[lm, 1] = (long)lhsShape[lm].Metadata.Range!.Value.Max - lhsShape[lm];
        var paddedLhs = IR.F.NN.Pad(lhs, Dimension.ConcatPadding(paddings), PadMode.Constant, padValue);
        var vectorizedLhs = IR.F.Tensors.Pack(VectorizeUtility.PadForVectorize(paddedLhs, lhsShape, lhsPackedAxes, lhsLanes, padValue, out var lhsPadNums, extraPadsA), lhsLanes, lhsPackedAxes);
        var vectorizedRhs = IR.F.Tensors.Pack(VectorizeUtility.PadForVectorize(rhs, rhsShape, rhsPackedAxes, rhsLanes, padValue, out var rhsPadNums, extraPadsB), rhsLanes, rhsPackedAxes);

        var matmul = IR.F.CustomNTT.MatMul(vectorizedLhs, vectorizedRhs, lhsPackedAxes, rhsPackedAxes, transA, transB, sbpsA, sbpsB, sbpsC, target.Cost, target.CSourcePath, target.FuncName, target.OutputDataType);

        var outRank = System.Math.Max(lhsShape.Length, rhsShape.Length);
        var lhsAlign = outRank - lhsShape.Length;
        var rhsAlign = outRank - rhsShape.Length;

        var unpackAxes = new List<int>();
        var unpadNums = new List<Dimension>();
        var unpackLanes = new List<int>();
        if (lhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.M))
        {
            var mPackIndex = Array.IndexOf(lhsPackedAxes, lm);
            unpackAxes.Add(outRank - 2);
            unpadNums.Add(lhsPadNums[mPackIndex]);
            unpackLanes.Add(lhsLaneSize);
        }

        if (rhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.N))
        {
            var nPackIndex = Array.IndexOf(rhsPackedAxes, rn);
            unpackAxes.Add(outRank - 1);
            unpadNums.Add(rhsPadNums[nPackIndex]);
            unpackLanes.Add(rhsLaneSize);
        }

        // output pack to [N', M, C128]
        if (lhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.K) && rhsPack.HasFlag(IR.NTT.VectorizedMatMul.VectorizeKind.K))
        {
            // var nPackIndex = Array.IndexOf(rhsPackedAxes, rn);
            unpackAxes.Add(outRank - 2);

            unpadNums.Add(extraPadsB[rn]);
            unpackLanes.Add(outLaneSize);
        }

        // FIXME: remove when tile graduality is aligned with Algo.
        unpadNums.Add(lm);

        Expr post = matmul;
        if (unpackAxes.Any())
        {
            var perm = Enumerable.Range(0, candidate.CheckedShape.Rank).ToArray();
            var outShape = candidate.CheckedShape.ToArray();
            if (transA || transB)
            {
                (perm[^2], perm[^1]) = (perm[^1], perm[^2]);
                (outShape[^1], outShape[^2]) = (outShape[^2], outShape[^1]);
            }

            post = IR.F.Tensors.Transpose(VectorizeUtility.SliceForVectorize(IR.F.Tensors.Unpack(matmul, unpackLanes.ToArray(), unpackAxes.ToArray()), outShape, unpadNums.ToArray()), perm);
        }

        if (post.CheckedType is not InvalidType)
        {
            rets.Add(post);
        }
    }

    private sealed record RuleContext(List<Expr> Results, IR.CustomNTT.MatMul MatMul, Expr Lhs, Expr Rhs, Expr Candidate, Shape LhsShape, Shape RhsShape)
    {
    }
}
