/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#ifdef SYS_MODE
#include <nncase/ntt/ukernels.h>
#include <nncase/ntt/arch/riscv64/arch_types.h>
#include <nncase/ntt/vector.h>
#include <riscv_vector.h>
#include "isa.h"
#include <nncase/ntt/ntt.h>

namespace nncase::ntt::ukernels {
template <bool AccumulateC, size_t M0Tile, size_t N0Tile, class TLhsElem,
          class TRhsElem, class TOutElem>
struct u_matmul<ukernels::matmul_vectorize_kind::vectorize_mkn, AccumulateC, false, false, M0Tile,
                N0Tile, TLhsElem, TRhsElem, TOutElem, true> {
    template <class TA, class TB, class TC>
    constexpr void operator()(const TA &a, const TB &b, TC &c0,
                              size_t K) noexcept {
        constexpr size_t me = TLhsElem::shape()[0];
        constexpr size_t ke = TLhsElem::shape()[1];
        constexpr tmma_dtype_t lhs_type = to_tmma_type<typename TLhsElem::element_type>();
        constexpr tmma_dtype_t rhs_type = to_tmma_type<typename TRhsElem::element_type>();
        constexpr tmma_dtype_t out_type = to_tmma_type<typename TOutElem::element_type>();
        constexpr bool transpose_b = (!std::is_same_v<typename TRhsElem::element_type, nncase::half> && ke == TRhsElem::shape()[1]) ? true : false;
        // uint32_t ping_pong = 0;
        uint32_t ping_pong_c = 0;
        for (size_t m = 0; m < M0Tile; m++) {
            for (size_t n = 0; n < N0Tile; n++) {
                if constexpr (AccumulateC) {
                    if (ping_pong_c == 0)
                        tload_c<me, out_type, 0b100>(reinterpret_cast<const char *>(&c0(m, n)));
                    else
                        tload_c<me, out_type, 0b101>(reinterpret_cast<const char *>(&c0(m, n)));
                }

                uint32_t count_k = K;
                uint32_t k = 0;
                while (count_k / 2) {
                    // ping
                    tload_a<me, lhs_type, 0b100>(reinterpret_cast<const char *>(&a(m, k)));
                    tload_b<TRhsElem::shape()[0], rhs_type, 0b100, transpose_b>(reinterpret_cast<const char *>(&b(k, n)));

                    if (ping_pong_c == 0) {
                        if (AccumulateC || k != 0)
                            tmma<me, lhs_type, rhs_type, 0b100, 0b100, true, true>();
                        else
                            tmma<me, lhs_type, rhs_type, 0b100, 0b100, false, true>();
                    }
                    else {
                        if (AccumulateC || k != 0)
                            tmma<me, lhs_type, rhs_type, 0b100, 0b101, true, true>();
                        else
                            tmma<me, lhs_type, rhs_type, 0b100, 0b101, false, true>();
                    }

                    // pong
                    tload_a<me, lhs_type, 0b101>(reinterpret_cast<const char *>(&a(m, k + 1)));
                    tload_b<TRhsElem::shape()[0], rhs_type, 0b101, transpose_b>(reinterpret_cast<const char *>(&b(k + 1, n)));

                    if (ping_pong_c == 0)
                        tmma<me, lhs_type, rhs_type, 0b101, 0b100, true, true>();
                    else
                        tmma<me, lhs_type, rhs_type, 0b101, 0b101, true, true>();

                    count_k -= 2;
                    k += 2;
                }

                while (count_k) {
                    tload_a<me, lhs_type, 0b100>(reinterpret_cast<const char *>(&a(m, k)));
                    tload_b<TRhsElem::shape()[0], rhs_type, 0b100, transpose_b>(reinterpret_cast<const char *>(&b(k, n)));

                    if (ping_pong_c == 0)
                        if (AccumulateC || k != 0)
                            tmma<me, lhs_type, rhs_type, 0b100, 0b100, true, true>();
                        else
                            tmma<me, lhs_type, rhs_type, 0b100, 0b100, false, true>();
                    else
                        if (AccumulateC || k != 0)
                            tmma<me, lhs_type, rhs_type, 0b100, 0b101, true, true>();
                        else
                            tmma<me, lhs_type, rhs_type, 0b100, 0b101, false, true>();

                    count_k--;
                }

                // for (size_t k = 0; k < K; k++) {
                //     if (ping_pong == 0) {
                //         tload_a<me, lhs_type, 0b100>(reinterpret_cast<const char *>(&a(m, k)));
                //         tload_b<TRhsElem::shape()[0], rhs_type, 0b100, transpose_b>(reinterpret_cast<const char *>(&b(k, n)));
                //     }
                //     else {
                //         tload_a<me, lhs_type, 0b101>(reinterpret_cast<const char *>(&a(m, k)));
                //         tload_b<TRhsElem::shape()[0], rhs_type, 0b101, transpose_b>(reinterpret_cast<const char *>(&b(k, n)));
                //     }

                //     if (ping_pong == 0) {
                //         if (ping_pong_c == 0) {
                //             if (AccumulateC || k != 0) {
                //                 tmma<me, lhs_type, rhs_type, 0b100, 0b100, true, true>();
                //             } else {
                //                 tmma<me, lhs_type, rhs_type, 0b100, 0b100, false, true>();
                //             }
                //         }
                //         else {
                //             if (AccumulateC || k != 0) {
                //                 tmma<me, lhs_type, rhs_type, 0b100, 0b101, true, true>();
                //             } else {
                //                 tmma<me, lhs_type, rhs_type, 0b100, 0b101, false, true>();
                //             }
                //         }
                //     }
                //     else
                //     {
                //         if (ping_pong_c == 0) {
                //             if (AccumulateC || k != 0) {
                //                 tmma<me, lhs_type, rhs_type, 0b101, 0b100, true, true>();
                //             } else {
                //                 tmma<me, lhs_type, rhs_type, 0b101, 0b100, false, true>();
                //             }
                //         }
                //         else {
                //             if (AccumulateC || k != 0) {
                //                 tmma<me, lhs_type, rhs_type, 0b101, 0b101, true, true>();
                //             } else {
                //                 tmma<me, lhs_type, rhs_type, 0b101, 0b101, false, true>();
                //             }
                //         }
                //     }

                //     ping_pong ^= 0x1;
                // }

                if (ping_pong_c == 0)
                    tstore<me, out_type, 0b100>(reinterpret_cast<char *>(&c0(m, n)));
                else
                    tstore<me, out_type, 0b101>(reinterpret_cast<char *>(&c0(m, n)));

                ping_pong_c ^= 0x1;
            }
        }
    }
};
} // namespace nncase::ntt::ukernels
#endif
