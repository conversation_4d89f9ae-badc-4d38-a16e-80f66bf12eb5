﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <packageSources>
    <clear />
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="Nncase.Libs" value="https://www.myget.org/F/magicallibs/api/v3/index.json" protocolVersion="3" />
    <add key="myget-xunit" value="https://www.myget.org/F/xunit/api/v3/index.json" />
    <add key="design-packages" value="tools/design-packages" />
  </packageSources>
  <activePackageSource>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="Nncase.Libs" value="https://www.myget.org/F/magicallibs/api/v3/index.json" protocolVersion="3" />
    <add key="myget-xunit" value="https://www.myget.org/F/xunit/api/v3/index.json" />
    <add key="design-packages" value="tools/design-packages" />
  </activePackageSource>
  <packageSourceMapping>
    <!-- key value for <packageSource> should match key values from <packageSources> element -->
    <packageSource key="nuget.org">
      <package pattern="*" />
    </packageSource>
    <packageSource key="Nncase.Libs">
      <package pattern="libortki*" />
      <package pattern="OrtKISharp" />
      <package pattern="AnyTensorFlow.NET" />
    </packageSource>
    <packageSource key="myget-xunit">
      <package pattern="xunit.v3.assert" />
    </packageSource>
    <packageSource key="design-packages">
      <package pattern="Nncase.SourceGenerator" />
    </packageSource>
  </packageSourceMapping>
  <disabledPackageSources>
    <add key="design-packages" value="true" />
  </disabledPackageSources>
</configuration>
