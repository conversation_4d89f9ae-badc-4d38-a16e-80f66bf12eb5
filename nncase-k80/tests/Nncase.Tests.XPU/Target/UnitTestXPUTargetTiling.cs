﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Runtime.CompilerServices;
using System.Text;
using System.Threading.Tasks;
using Microsoft.Extensions.DependencyInjection;
using NetFabric.Hyperlinq;
using Nncase.CodeGen;
using Nncase.CodeGen.XPU;
using Nncase.IR;
using Nncase.IR.F;
using Nncase.IR.Imaging;
using Nncase.IR.Math;
using Nncase.IR.NN;
using Nncase.IR.Tensors;
using Nncase.Runtime.Interop;
using Nncase.Targets;
using Nncase.Tests.TestFixture;
using Nncase.Utilities;
using Xunit;
using static Nncase.IR.F.Tensors;
using GetItem = Nncase.IR.Tensors.GetItem;

namespace Nncase.Tests.XPU.Targets;

[AutoSetupTestMethod(InitSession = true)]
public class UnitTestXPUTargetTiling : TestClassBase
{
    public UnitTestXPUTargetTiling()
    {
        DefaultTargetName = XPUTarget.Kind;
        CompileOptions.TargetOptions = new NTTTargetOptions
        {
            Hierarchies = new[] { new[] { 1, 2, 8, 4, 4 } },
            HierarchyNames = "cdyxt",
            HierarchySizes = new long[] { 512 * (int)MathF.Pow(2, 20), 1 * (int)MathF.Pow(2, 20) },
            MemoryCapacities = new[] { 256 * (int)MathF.Pow(2, 10), 128 * (int)MathF.Pow(2, 20) },
            MemoryBandWidths = new[] { 64, 32 },
            HierarchyLatencies = new[] { 10000, 10000, 10000, 10000, 10000 },
            UnifiedMemoryArch = false,
            Vectorize = true,
            CustomOpScheme = string.Empty,
            HierarchyKind = HierarchyKind.SMT,
        };
#if DEBUG
        CompileOptions.DumpFlags = Diagnostics.DumpFlags.PassIR | Diagnostics.DumpFlags.Rewrite | Diagnostics.DumpFlags.CodeGen | Diagnostics.DumpFlags.EGraphCost | Diagnostics.DumpFlags.Tiling;
#endif
    }

    [Theory]

    // [ClassData(typeof(TilingCaseConv2D))]
    [ClassData(typeof(TilingCaseBinaryMul))]
    [ClassData(typeof(TilingCaseUnary))]
    [ClassData(typeof(TilingCaseUnaryUnary))]
    [ClassData(typeof(TilingCaseMatmul))]
    [ClassData(typeof(TilingCaseLayerNorm))]
    [ClassData(typeof(TilingCaseGather))]
    [ClassData(typeof(TilingCaseSoftmax))]
    [ClassData(typeof(TilingCaseSlice))]
    [ClassData(typeof(TilingCaseConcat))]
    [ClassData(typeof(TilingCaseTranspose))]
    [ClassData(typeof(TilingCaseReshape1))]
    [ClassData(typeof(TilingCaseReshape2))]
    [ClassData(typeof(TilingCaseReduce))]

    // [ClassData(typeof(TilingCaseReduceArg))]
    // [ClassData(typeof(TilingCaseReduceArg2))]
    // [ClassData(typeof(TilingCaseInstanceNorm))]
    // [ClassData(typeof(TilingCaseEncoderTail))]
    [ClassData(typeof(TilingCaseResize))]
    [ClassData(typeof(TilingCaseCast))]
    [ClassData(typeof(TilingCaseBinaryAdd))]
    [ClassData(typeof(TilingCaseGatherBinary))]
    [ClassData(typeof(TilingCaseLayerNormBinary))]
    [ClassData(typeof(TilingCaseExpand))]
    [ClassData(typeof(TilingCaseClamp))]
    [ClassData(typeof(TilingCaseWhere))]
    [ClassData(typeof(TilingCaseMatmulUnaryNorm))]
    [ClassData(typeof(TilingCaseMatmulUnaryBroadCast))]
    [ClassData(typeof(TilingCaseSDMHA))]
    [ClassData(typeof(TilingCaseLLaMaMHA))]

    // [ClassData(typeof(TilingCaseScatterND))]
    // [ClassData(typeof(TilingCaseVAEDecRes))]
    // [ClassData(typeof(TilingCaseMultiFunc))]
    // [ClassData(typeof(TilingCaseLLaMa8B))]
    public async Task TestXpuFunction(Function main, string name, Tensor[] inputs)
    {
        CompileOptions.DumpDir = Path.Join(CompileOptions.DumpDir, main.Name);
        var module = new IR.IRModule(main);
        var parameterLength = main.Parameters.Length;
        using (new Diagnostics.DumpScope(string.Empty, CompileOptions.DumpFlags))
        {
#if DEBUG
            for (var i = 0; i < parameterLength; i++)
            {
                using (var fs = Diagnostics.DumpScope.Current.OpenFile($"input_{i}.bin"))
                {
                    fs.Write(inputs[i].BytesBuffer);
                }
            }

            for (var i = 0; i < inputs.Length - parameterLength; i++)
            {
                using (var fs = Diagnostics.DumpScope.Current.OpenFile($"output_{i}.bin"))
                {
                    fs.Write(inputs[parameterLength + i].BytesBuffer);
                }
            }
#endif
            await Compile(module);
            var sysMod = CSourceUtilities.GetRuntimeMode();
            if (sysMod == RuntimeMode.Local)
            {
                var outputs = Testing.RunKModel(Path.Join(Diagnostics.DumpScope.Current.Directory, "test.kmodel"), Diagnostics.DumpScope.Current.Directory, inputs[..parameterLength]).AsTensors();
#if DEBUG
                for (int i = 0; i < outputs.Length; i++)
                {
                    using (var fs = Diagnostics.DumpScope.Current.OpenFile($"actual_{i}.bin"))
                    {
                        fs.Write(outputs[i].BytesBuffer);
                    }
                }
#endif
                var cos = Tests.Comparator.CosSimilarity(outputs, inputs[parameterLength..]);
                System.Console.WriteLine(string.Join("\n", cos.Select((c, i) => $"the outputs[{i}] cos is {c}!")));
                Assert.True(cos.All(c => c > 0.999), string.Join("\n", cos.Select((c, i) => $"the outputs[{i}] cos is {c}!")));
            }
            else if (sysMod == RuntimeMode.UserMode)
            {
                var qemuPath = Environment.GetEnvironmentVariable("QEMU_PATH");
                if (string.IsNullOrEmpty(qemuPath))
                {
                    throw new FileNotFoundException("QEMU_PATH is not set for user mode.");
                }

                var xpuTestPath = Environment.GetEnvironmentVariable("XPU_TEST");
                if (string.IsNullOrEmpty(xpuTestPath))
                {
                    throw new FileNotFoundException("XPU_TEST is not set for user mode.");
                }

                var sysRoot = Environment.GetEnvironmentVariable("SYS_ROOT");
                if (string.IsNullOrEmpty(sysRoot))
                {
                    throw new FileNotFoundException("SYS_ROOT is not set for user mode.");
                }

                for (var i = 0; i < parameterLength; i++)
                {
                    using (var fs = Diagnostics.DumpScope.Current.OpenFile($"input_{i}.bin"))
                    {
                        fs.Write(inputs[i].BytesBuffer);
                    }
                }

                for (var i = 0; i < inputs.Length - parameterLength; i++)
                {
                    using (var fs = Diagnostics.DumpScope.Current.OpenFile($"output_{i}.bin"))
                    {
                        fs.Write(inputs[parameterLength + i].BytesBuffer);
                    }
                }

                var args = "-cpu canaan-k800-nano"
                + $" -L {sysRoot} "
                + xpuTestPath + " "
                + Path.Join(Diagnostics.DumpScope.Current.Directory, "test.kmodel");
                for (var i = 0; i < parameterLength; i++)
                {
                    args += $" {Path.Join(Diagnostics.DumpScope.Current.Directory, $"input_{i}.bin")}";
                }

                for (var i = 0; i < inputs.Length - parameterLength; i++)
                {
                    args += $" {Path.Join(Diagnostics.DumpScope.Current.Directory, $"output_{i}.bin")}";
                }

                var errMsg = new StringBuilder();
                using (var errWriter = new StringWriter(errMsg))
                {
                    using (var proc = new Process())
                    {
                        proc.StartInfo.FileName = Path.Join(qemuPath, "qemu-riscv64");
                        proc.StartInfo.Arguments = args;
                        proc.StartInfo.RedirectStandardError = true;
                        proc.ErrorDataReceived += (sender, e) => errWriter.WriteLine(e.Data);
                        proc.Start();
                        proc.BeginErrorReadLine();
                        proc.WaitForExit();
                        if (proc.ExitCode != 0)
                        {
                            throw new InvalidOperationException(errMsg.ToString());
                        }
                    }
                }
            }
            else
            {
                for (var i = 0; i < parameterLength; i++)
                {
                    using (var fs = Diagnostics.DumpScope.Current.OpenFile($"input_{i}.bin"))
                    {
                        fs.Write(inputs[i].BytesBuffer);
                    }
                }

                for (var i = 0; i < inputs.Length - parameterLength; i++)
                {
                    using (var fs = Diagnostics.DumpScope.Current.OpenFile($"output_{i}.bin"))
                    {
                        fs.Write(inputs[parameterLength + i].BytesBuffer);
                    }
                }

                throw new NotImplementedException("Can not invoke kmodel directly for system mode.");
            }
        }
    }

    private async Task Compile(IRModule module)
    {
        var compiler = CompileSession.Compiler;
        compiler.ImportIRModule(module);
        await compiler.CompileAsync();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("test.kmodel"))
        {
            compiler.Gencode(fs);
        }
    }
}

internal class TilingCaseUnaryUnary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseUnaryUnary()
    {
        var shape = new[] { 1, 224, 2048 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));

        var main = new Function("unary_unary", IR.F.Math.Unary(UnaryOp.Abs, IR.F.Math.Unary(UnaryOp.Cos, IR.F.NN.Swish(input, 1f))), new[] { input });
        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseSlice : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseSlice()
    {
        var shape = new[] { 1, 1, 64, 128 };
        var in_a = new Var("in_a", new TensorType(DataTypes.Float32, shape));

        var main = new Function("slice", IR.F.Tensors.Slice(in_a, new[] { 64 }, new[] { 128 }, new[] { 3 }, new[] { 1 }), new[] { in_a });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { in_a, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseConcat : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseConcat()
    {
        var in_a_shape = new[] { 1, 64, 384, 64 };
        var in_b_shape = new[] { 1, 64, 384, 64 };
        var in_a = new Var("in_a", new TensorType(DataTypes.Float32, in_a_shape));
        var in_b = new Var("in_b", new TensorType(DataTypes.Float32, in_b_shape));

        var main = new Function("concat", IR.F.Tensors.Concat(new IR.Tuple(in_a, in_b), 3), new[] { in_a, in_b });

        var input_a_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, in_a_shape).Evaluate().AsTensor();
        var input_b_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 4, in_b_shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { in_a, Value.FromTensor(input_a_tensor) },
            { in_b, Value.FromTensor(input_b_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_a_tensor, input_b_tensor, output });
    }
}

internal sealed class TilingCaseMatmulLayerNorm : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMatmulLayerNorm()
    {
        var hid_in = new Var("hidden_in", new TensorType(DataTypes.Float32, new[] { 1, 384, 8192 }));

        Fusion fusion;
        {
            Expr scale = IR.F.Tensors.ConstantOfShape(new[] { 8192 }, 1.0f).Evaluate().AsTensor();
            Expr bias = IR.F.Tensors.ConstantOfShape(new[] { 8192 }, 0.0f).Evaluate().AsTensor();
            Expr weights = IR.F.Random.Normal(DataTypes.Float32, new[] { 1, 64, 8192, 128 }).Evaluate().AsTensor();
            _ = IR.F.Random.Normal(DataTypes.Float32, new[] { 384, 128 }).Evaluate().AsTensor();

            var fin = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 384, 8192 }));
            var v0 = new Call(new IR.XPU.XPUKernelOp(new IR.NN.LayerNorm(2, 1e-6f, false, false)), fin, scale, bias);
            var v1 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Unsqueeze()), v0, (Expr)new[] { 0 });
            var v2 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.MatMul(DataTypes.Float32)), v1, weights);
            var v3 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.Unary(UnaryOp.Exp)), v2);

            fusion = new Fusion("kernel", XPUTarget.Kind, v3, fin);
        }

        var main = new Function("matmul_layernorm", new Call(fusion, hid_in), new[] { hid_in });
        Add(main, main.Name, Array.Empty<Tensor>());
    }
}

#if false
internal sealed class TilingCaseMHA : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMHA()
    {
        var hid_in = new Var("hidden_in", new TensorType(DataTypes.Float32, new[] { 1, 384, 8192 }));
        var pos_ids = new Var("position_ids", new TensorType(DataTypes.Int64, new[] { 1, 384 }));

        Fusion fusion;
        {
            var scale = IR.F.Tensors.ConstantOfShape(new[] { 8192 }, 1.0f).Evaluate().AsTensor();
            var bias = IR.F.Tensors.ConstantOfShape(new[] { 8192 }, 0.0f).Evaluate().AsTensor();
            var weights = IR.F.Random.Normal(DataTypes.Float32, new[] { 1, 64, 8192, 128 }).Evaluate().AsTensor();
            var gdata = IR.F.Random.Normal(DataTypes.Float32, new[] { 384, 128 }).Evaluate().AsTensor();

            var fin = new Var("input", new TensorType(DataTypes.Float32, new[] { 1, 384, 8192 }));
            var fin2 = new Var("input2", new TensorType(DataTypes.Int64, new[] { 1, 384 }));
            var v0 = new Call(new IR.XPU.XPUKernelOp(new IR.NN.LayerNorm(2, 1e-6f, false)), fin, scale, bias);
            var v1 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Unsqueeze()), v0, new[] { 0 });
            var v2 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.MatMul()), v1, weights);
            var v3 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Slice()), v2, new[] { 64 }, new[] { 128 }, new[] { 3 }, new[] { 1 });
            var v4 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.Unary(UnaryOp.Neg)), v3);
            var v5 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Slice()), v2, new[] { 0 }, new[] { 64 }, new[] { 3 }, new[] { 1 });
            var v6 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Concat(3)), new IR.Tuple(v4, v5));

            var v7 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Gather(0)), gdata, fin2);
            var v8 = new Call(new IR.XPU.XPUKernelOp(new IR.Tensors.Unsqueeze()), v7, new[] { 0 });

            var v9 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.Binary(BinaryOp.Mul)), v2, v8);
            var v10 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.Binary(BinaryOp.Mul)), v6, v8);
            var v11 = new Call(new IR.XPU.XPUKernelOp(new IR.Math.Binary(BinaryOp.Add)), v9, v10);

            fusion = new Fusion("kernel", XPUTarget.Kind, v11, fin, fin2);
        }

        var main = new Function("mha_qk", new Call(fusion, hid_in, pos_ids), new[] { hid_in, pos_ids });
        Add(main,main.Name, Array.Empty<Tensor>());
    }
}
#endif

internal sealed class TilingCaseBinaryAdd : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseBinaryAdd()
    {
        var lhsShape = new[] { 1, 77, 768 };
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhsShape = new[] { 1, 77, 768 };
        var rhs = new Var("rhs", new TensorType(DataTypes.Float32, rhsShape));

        var main = new Function("binary_add", IR.F.Math.Add(lhs, rhs), new[] { lhs, rhs });

        var lhs_tensor = IR.F.Random.Uniform(DataTypes.Float32, 1, 0, 2, lhsShape).Evaluate().AsTensor();
        var rhs_tensor = IR.F.Random.Uniform(DataTypes.Float32, 1, 0, 2, rhsShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
            { rhs, Value.FromTensor(rhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, rhs_tensor, output });
    }
}

internal sealed class TilingCaseBinaryMul : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseBinaryMul()
    {
        var lhsShape = new[] { 1, 64, 384, 128 };
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhsShape = new[] { 1, 1, 384, 128 };
        var rhs = new Var("rhs", new TensorType(DataTypes.Float32, rhsShape));

        var main = new Function("binary_mul", IR.F.Math.Binary(BinaryOp.Mul, lhs, rhs), new[] { lhs, rhs });

        var lhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, lhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(lhs_tensor.BytesBuffer);
        }

        var rhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, rhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_1.bin"))
        {
            fs.Write(rhs_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
            { rhs, Value.FromTensor(rhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, rhs_tensor, output });
    }
}

internal sealed class TilingCaseUnary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseUnary()
    {
        var shape = new[] { 1, 384, 2048 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));
        var main = new Function("unary", IR.F.Math.Unary(UnaryOp.Sin, input), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseMultiFunc : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMultiFunc()
    {
        var shape = new[] { 1, 384, 2048 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));

        var fin1 = new Var("input1", new TensorType(DataTypes.Float32, shape));
        var fusion1 = new Fusion("unary1_kernel", XPUTarget.Kind, IR.F.Math.Unary(UnaryOp.Abs, fin1), fin1);
        var v0 = new Call(fusion1, input);

        var fin2 = new Var("input2", new TensorType(DataTypes.Float32, shape));
        var fusion2 = new Fusion("unary2_kernel", XPUTarget.Kind, IR.F.Math.Unary(UnaryOp.Sin, fin2), fin2);
        var v1 = new Call(fusion2, v0);

        var main = new Function("unary_unary_diff_func", v1, new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>(ReferenceEqualityComparer.Instance)
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = CompilerServices.Evaluate(main.Body, feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseMatmul : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMatmul()
    {
        var lhsShape = new[] { 1, 1, 384, 128 };
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhsShape = new[] { 1, 1, 128, 128 };
        var rhs = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, rhsShape).Evaluate().AsTensor().Cast<float>();

        var main = new Function("matmul", IR.F.Math.MatMul(lhs, rhs), new[] { lhs });

        var lhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, lhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(lhs_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, output });
    }
}

internal sealed class TilingCaseMatmulUnaryNorm : TilingCaseMatmulUnary
{
    public override int[] LhsShape => new[] { 1, 64, 384, 128 };

    public override int[] RhsShape => new[] { 1, 1, 128, 384 };

    public override string FuncName => "matmul_unary_norm";
}

internal sealed class TilingCaseMatmulUnaryBroadCast : TilingCaseMatmulUnary
{
    public override int[] LhsShape => new[] { 1, 77, 768 };

    public override int[] RhsShape => new[] { 768, 768 };

    public override string FuncName => "matmul_unary_broadcast";
}

internal abstract class TilingCaseMatmulUnary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMatmulUnary()
    {
        var lhsShape = LhsShape;
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhsShape = RhsShape;
        Expr rhs = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, rhsShape).Evaluate().AsTensor();

        var v0 = new Call(new IR.Math.MatMul(DataTypes.Float32), lhs, rhs);
        var v1 = new Call(new IR.Math.Unary(UnaryOp.Neg), v0);

        var main = new Function(FuncName, v1, new[] { lhs });

        var lhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, lhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(lhs_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, output });
    }

    public abstract int[] LhsShape { get; }

    public abstract int[] RhsShape { get; }

    public abstract string FuncName { get; }
}

internal sealed class TilingCaseLayerNorm : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseLayerNorm()
    {
        var shape = new[] { 1, 384, 8192 };
        int axis = 2;
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));
        Expr scale = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new[] { shape[2] }).Evaluate().AsTensor();
        Expr bias = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 3, new[] { shape[2] }).Evaluate().AsTensor();

        var main = new Function("layernorm", new Call(new IR.NN.LayerNorm(axis, 1e-5f, false, false), input, scale, bias), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseLayerNormBinary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseLayerNormBinary()
    {
        var shape = new[] { 1, 77, 768 };
        int axis = 2;
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));
        Expr scale = IR.F.Random.Normal(DataTypes.Float32, new[] { shape[2] }).Evaluate().AsTensor();
        Expr bias = IR.F.Random.Normal(DataTypes.Float32, new[] { shape[2] }).Evaluate().AsTensor();

        var v0 = new Call(new IR.NN.LayerNorm(axis, 1e-5f, true, false), input, scale, bias);
        var v1 = IR.F.NN.Swish(v0, 1.602f);
        var v2 = IR.F.Math.Binary(BinaryOp.Add, input, v1);
        var main = new Function("layernorm_binary", v2, new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseInstanceNorm : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseInstanceNorm()
    {
        var shape = new[] { 1, 32, 65536 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));
        Expr scale = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new[] { shape[1] }).Evaluate().AsTensor();
        Expr bias = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 3, new[] { shape[1] }).Evaluate().AsTensor();

        var main = new Function("instance_norm", new Call(new IR.NN.InstanceNormalization(), input, scale, bias, (Expr)1e-5f), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseGather : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseGather()
    {
        var inputShape = new[] { 384, 128 };
        var axisShape = new[] { 1, 384 };
        int axis = 0;
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));
        var indices = IR.F.Random.Uniform(DataTypes.Int64, 384, 0, 2, axisShape).Evaluate().AsTensor();

        var main = new Function("gather", IR.F.Tensors.Gather(input, axis, indices), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseGatherBinary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseGatherBinary()
    {
        var inputShape = new[] { 49408, 768 };
        var indicesShape = new[] { 1, 77 };
        int axis = 0;
        var input = Const.FromValue(IR.F.Random.Normal(DataTypes.Float32, 1, 0, 2, inputShape).Evaluate());
        var indices = new Var("indices", new TensorType(DataTypes.Int32, indicesShape));

        var v0 = new Call(new IR.Tensors.Gather(axis), input, indices);
        var v1 = new Call(new IR.Math.Binary(BinaryOp.Add), v0, (Expr)IR.F.Random.Normal(DataTypes.Float32, new[] { 1, 77, 768 }).Evaluate().AsTensor());

        var main = new Function("gather_binary", v1, new[] { indices });

        var input_tensor = IR.F.Random.Uniform(DataTypes.Int32, 1, 0, 2, indicesShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { indices, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseSoftmax : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseSoftmax()
    {
        var inputShape = new[] { 12, 77, 77 };
        int axis = 2;
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));

        var main = new Function("softmax", IR.F.NN.Softmax(input, axis), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseTranspose : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseTranspose()
    {
        var shape = new[] { 1, 2, 64, 128 };
        var in_a = new Var("in_a", new TensorType(DataTypes.Float32, shape));
        var perm = new[] { 0, 2, 3, 1 };

        var main = new Function("transpose", IR.F.Tensors.Transpose(in_a, perm), new[] { in_a });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { in_a, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();
        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseReshape1 : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseReshape1()
    {
        var inputShape = new[] { 1, 384, 128 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));
        var newShape = new[] { 1, 1, 384, 128 };

        var main = new Function("reshape1", IR.F.Tensors.Reshape(input, newShape), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseReshape2 : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseReshape2()
    {
        var inputShape = new[] { 1, 384, 64, 128 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));
        var newShape = new[] { 1, 384, 8192 };

        var main = new Function("reshape2", IR.F.Tensors.Reshape(input, newShape), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseReduceArg : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseReduceArg()
    {
        var inputShape = new[] { 1, 77 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));

        var v0 = new Call(new IR.Math.ReduceArg(ReduceArgOp.ArgMax, DataTypes.Int64), input, new DimConst(1), (Expr)false, (Expr)false);
        var main = new Function("argmax", v0, new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseReduce : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseReduce()
    {
        var inputShape = new[] { 2, 77 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));

        var main = new Function("reduce_max", new Call(new IR.Math.Reduce(ReduceOp.Max), input, new RankedShape(1), (Expr)0f, (Expr)false), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseReduceArg2 : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseReduceArg2()
    {
        var inputShape = new[] { 32, 64, 128, 512 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));

        var main = new Function("argmax2", new Call(new IR.Math.ReduceArg(ReduceArgOp.ArgMax, DataTypes.Int64), input, new DimConst(2), (Expr)false, (Expr)false), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseEncoderTail : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseEncoderTail()
    {
        var v_16 = new Var("v16", new TensorType(DataTypes.Int32, new[] { 1, 77 }));
        var v_17 = new Var("v17", new TensorType(DataTypes.Float32, new[] { 1, 77, 768 }));
        var v0 = IR.F.NN.LayerNorm(2, 1E-05f, v_17, IR.F.Random.Normal(DataTypes.Float32, 2, 4, 3, new[] { 768 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 4, 3, new[] { 768 }).Evaluate().AsTensor(), true); // f32[1,77,768]
        var v1 = IR.F.Tensors.Reshape(v0, new[] { 77, 768 }); // f32[77,768]
        var v2 = IR.F.Tensors.ReduceArg(ReduceArgOp.ArgMax, DataTypes.Int64, v_16, 1, false, false); // i64[1]

        // var v3 = IR.F.Math.Binary(BinaryOp.Add, v2, 1L); // i64[1]
        var v4 = IR.F.Tensors.Gather(v1, 0, v2); // f32[1,768]
        var main = new Function("encoderTail", new IR.Tuple(v0, v4), new[] { v_16, v_17 });

        var input_16 = IR.F.Tensors.Cast(IR.F.Random.Uniform(DataTypes.Int32, 60, 1, 2, new[] { 1, 77 }), DataTypes.Int32).Evaluate().AsTensor();
        var input_17 = IR.F.Random.Normal(DataTypes.Float32, 2, 4, 3, new[] { 1, 77, 768 }).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { v_16, Value.FromTensor(input_16) },
            { v_17, Value.FromTensor(input_17) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensors();

        Add(main, main.Name, new[] { input_16, input_17 }.Concat(output).ToArray());
    }
}

internal sealed class TilingCaseConv2D : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseConv2D()
    {
        var inputShape = new long[] { 1, 4, 64, 64 };
        var input = new Var("input", new TensorType(DataTypes.Float32, inputShape));
        var weightsShape = new long[] { 512, 4, 3, 3 };
        var weights = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, weightsShape).Evaluate().AsTensor().Cast<float>();
        var bias = Tensor.Zeros<float>(weightsShape.Take(1).ToArray());
        var stride = new int[] { 1, 1 };
        var padding = new int[,] { { 1, 1 }, { 1, 1 } };
        var dilation = new int[] { 1, 1 };
        var groups = 1;
        var fusedClamp = new float[] { float.NegativeInfinity, float.PositiveInfinity };

        var main = new Function("conv2d", IR.F.NN.Conv2D(input, weights, bias, stride, padding, dilation, PadMode.Constant, groups, fusedClamp), new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, inputShape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
        {
            { input, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseResize : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseResize()
    {
        var shape = new[] { 1, 512, 64, 64 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));

        var v0 = new Call(new IR.Imaging.ResizeImage(ImageResizeMode.NearestNeighbor, ImageResizeTransformationMode.Asymmetric, ImageResizeNearestMode.Floor, false), input, (Expr)Array.Empty<float>(), new RankedShape(1, 512, 128, 128), None.Default, None.Default, None.Default);
        var main = new Function("nearest_resize", v0, new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
            {
                { input, Value.FromTensor(input_tensor) },
            };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseCast : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseCast()
    {
        var shape = new[] { 1, 2 };
        var input = new Var("input", new TensorType(DataTypes.Int64, shape));

        var v0 = new Call(new IR.Tensors.Cast(DataTypes.Float32, CastMode.KDefault, Array.Empty<int>()), input);
        var main = new Function("cast", v0, new[] { input });

        var input_tensor = IR.F.Random.Uniform(DataTypes.Int64, 100, 1, 2, shape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(input_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
            {
                { input, Value.FromTensor(input_tensor) },
            };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseSDMHA : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseSDMHA()
    {
        var fin = new Var("var_4", new TensorType(DataTypes.Float32, new[] { 1, 77, 768 }));
        var v0 = IR.F.NN.LayerNorm(2, 1E-05f, fin, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), true); // f32[1,77,768]
        var w = IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 768, 64 }).Evaluate().AsTensor();
        var v1 = IR.F.Math.MatMul(v0, w); // f32[12,77,64]
        var v2 = IR.F.Math.Binary(BinaryOp.Add, v1, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 1, 64 }).Evaluate().AsTensor()); // f32[12,77,64]
        var v3 = IR.F.Math.Binary(BinaryOp.Mul, v2, new[] { 0.125f }); // f32[12,77,64]
        var v4 = IR.F.Math.MatMul(v0, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 768, 64 }).Evaluate().AsTensor()); // f32[12,77,64]
        var v5 = IR.F.Math.Binary(BinaryOp.Add, v4, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 1, 64 }).Evaluate().AsTensor()); // f32[12,77,64]
        var v6 = IR.F.Tensors.Transpose(v5, new[] { 0, 2, 1 }); // f32[12,64,77]
        var v7 = IR.F.Math.MatMul(v3, v6); // f32[12,77,77]
        var v8 = IR.F.Math.Binary(BinaryOp.Add, v7, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1, 77, 77 }).Evaluate().AsTensor()); // f32[12,77,77]
        var v9 = IR.F.NN.Softmax(v8, 2); // f32[12,77,77]
        var v10 = IR.F.Math.MatMul(v0, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 768, 64 }).Evaluate().AsTensor()); // f32[12,77,64]
        var v11 = IR.F.Math.Binary(BinaryOp.Add, v10, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 12, 1, 64 }).Evaluate().AsTensor()); // f32[12,77,64]
        var v12 = IR.F.Math.MatMul(v9, v11); // f32[12,77,64]
        var v13 = IR.F.Tensors.Transpose(v12, new[] { 1, 0, 2 }); // f32[77,12,64]
        var v14 = IR.F.Tensors.Reshape(v13, new[] { 1L, 77L, 768L }); // f32[1,77,768]
        var v15 = IR.F.Math.MatMul(v14, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768, 768 }).Evaluate().AsTensor()); // f32[1,77,768]
        var v16 = IR.F.Math.Binary(BinaryOp.Add, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), v15); // f32[1,77,768]
        var v17 = IR.F.Math.Binary(BinaryOp.Add, fin, v16); // f32[1,77,768]
        var v18 = IR.F.NN.LayerNorm(2, 1E-05f, v17, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), true); // f32[1,77,768]
        var v19 = IR.F.Math.MatMul(v18, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768, 3072 }).Evaluate().AsTensor()); // f32[1,77,3072]
        var v20 = IR.F.Math.Binary(BinaryOp.Add, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 3072 }).Evaluate().AsTensor(), v19); // f32[1,77,3072]
        var v21 = IR.F.NN.Swish(v20, 1.702f); // f32[1,77,3072]
        var v22 = IR.F.Math.MatMul(v21, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 3072, 768 }).Evaluate().AsTensor()); // f32[1,77,768]
        var v23 = IR.F.Math.Binary(BinaryOp.Add, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 768 }).Evaluate().AsTensor(), v22); // f32[1,77,768]
        var v24 = IR.F.Math.Binary(BinaryOp.Add, v17, v23); // f32[1,77,768]

        var main = new Function("sd_mha", new IR.Tuple(v17, v23, v24), new[] { fin });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1, 77, 768 }).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { fin, Value.FromTensor(input_tensor) },
            };
        var outputs = main.Body.Evaluate(feedDict).AsTensors();

        Add(main, main.Name, new[] { input_tensor }.Concat(outputs).ToArray());
    }
}

internal sealed class TilingCaseLLaMa8B : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseLLaMa8B()
    {
        var v1 = new Var("var_4491828", new TensorType(DataTypes.Float32, new[] { 1, 8, 0, 128 }));
        var v2 = new Var("var_4491826", new TensorType(DataTypes.Int64, new[] { 1, 384 }));
        var v3 = new Call(new Gather(axis: 0), new Expr[] { IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1024, 4096 }), v2 }); // f32[1,384,4096]
        var v4 = new Call(new Unary(UnaryOp.Square), new Expr[] { v3 }); // f32[1,384,4096]
        var v5 = new Call(new Reduce(ReduceOp.Mean), new Expr[] { v4, new int[] { 2 }, 0, true }); // f32[1,384,1]
        var v6 = new Call(new Binary(BinaryOp.Add), new Expr[] { v5, new float[] { 1E-05f } }); // f32[1,384,1]
        var v7 = new Call(new Unary(UnaryOp.Rsqrt), new Expr[] { v6 }); // f32[1,384,1]
        var v8 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v3, v7 }); // f32[1,384,4096]
        var v9 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v8, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }) }); // f32[1,384,4096]
        var v10 = new Call(new Binary(BinaryOp.Add), new Expr[] { v9, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }) }); // f32[1,384,4096]
        var v11 = new Call(new Unsqueeze(), new Expr[] { v10, new int[] { 1 } }); // f32[1,1,384,4096]
        var v12 = new Call(new MatMul(DataTypes.Float32), new Expr[] { v11, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 8, 4096, 128 }) }); // f32[1,8,384,128]
        var v13 = new IR.Tuple(new Expr[] { v1, v12 }); // (f32[1,8,0,128], f32[1,8,384,128])

        var v14 = new Call(new Concat(axis: 2), new BaseExpr[] { v13 }); // f32[1,8,384,128]
        var v15 = new Call(new Reshape(), new Expr[] { v14, new int[] { 1, 8, 1, 384, 128 } }); // f32[1,8,1,384,128]
        var v16 = new Call(new Expand(), new Expr[] { v15, new long[] { 1L, 8L, 4L, 384L, 128L } }); // f32[1,8,4,384,128]
        var v17 = new Call(new Reshape(), new Expr[] { v16, new long[] { 1L, 32L, 384L, 128L } }); // f32[1,32,384,128]
        var v18 = new Var("var_4491829", new TensorType(DataTypes.Float32, new[] { 1, 8, 0, 128 }));
        var v19 = new Call(new Unsqueeze(), new Expr[] { v10, new int[] { 1 } }); // f32[1,1,384,4096]
        var v20 = new Call(new MatMul(DataTypes.Float32), new Expr[] { v19, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 8, 4096, 128 }) }); // f32[1,8,384,128]
        var v21 = new Var("var_4491827", new TensorType(DataTypes.Float32, new[] { 1, 384, 64 }));
        var v22 = new IR.Tuple(new Expr[] { v21, v21 }); // (f32[1,384,64], f32[1,384,64])

        var v23 = new Call(new Concat(axis: 2), new BaseExpr[] { v22 }); // f32[1,384,128]
        var v24 = new Call(new Unary(UnaryOp.Cos), new Expr[] { v23 }); // f32[1,384,128]
        var v25 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v24, new float[,,] { { { 1f } } } }); // f32[1,384,128]
        var v26 = new Call(new Reshape(), new Expr[] { v25, new int[] { 1, 1, 384, 128 } }); // f32[1,1,384,128]
        var v27 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v20, v26 }); // f32[1,8,384,128]
        var v28 = new Call(new Slice(), new Expr[] { v20, new long[] { 64L }, new long[] { 128L }, new long[] { 3L }, new long[] { 1L } }); // f32[1,8,384,64]
        var v29 = new Call(new Unary(UnaryOp.Neg), new Expr[] { v28 }); // f32[1,8,384,64]
        var v30 = new Call(new Slice(), new Expr[] { v20, new long[] { 0L }, new long[] { 64L }, new long[] { 3L }, new long[] { 1L } }); // f32[1,8,384,64]
        var v31 = new IR.Tuple(new Expr[] { v29, v30 }); // (f32[1,8,384,64], f32[1,8,384,64])

        var v32 = new Call(new Concat(axis: 3), new BaseExpr[] { v31 }); // f32[1,8,384,128]
        var v33 = new Call(new Unary(UnaryOp.Sin), new Expr[] { v23 }); // f32[1,384,128]
        var v34 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v33, new float[,,] { { { 1f } } } }); // f32[1,384,128]
        var v35 = new Call(new Reshape(), new Expr[] { v34, new int[] { 1, 1, 384, 128 } }); // f32[1,1,384,128]
        var v36 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v32, v35 }); // f32[1,8,384,128]
        var v37 = new Call(new Binary(BinaryOp.Add), new Expr[] { v27, v36 }); // f32[1,8,384,128]
        var v38 = new IR.Tuple(new Expr[] { v18, v37 }); // (f32[1,8,0,128], f32[1,8,384,128])

        var v39 = new Call(new Concat(axis: 2), new BaseExpr[] { v38 }); // f32[1,8,384,128]
        var v40 = new Call(new Reshape(), new Expr[] { v39, new int[] { 1, 8, 1, 384, 128 } }); // f32[1,8,1,384,128]
        var v41 = new Call(new Expand(), new Expr[] { v40, new long[] { 1L, 8L, 4L, 384L, 128L } }); // f32[1,8,4,384,128]
        var v42 = new Call(new Reshape(), new Expr[] { v41, new long[] { 1L, 32L, 384L, 128L } }); // f32[1,32,384,128]
        var v43 = new Call(new Unsqueeze(), new Expr[] { v10, new int[] { 1 } }); // f32[1,1,384,4096]
        var v44 = new Call(new MatMul(DataTypes.Float32), new Expr[] { v43, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 32, 4096, 128 }) }); // f32[1,32,384,128]
        var v45 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v44, v26 }); // f32[1,32,384,128]
        var v46 = new Call(new Slice(), new Expr[] { v44, new long[] { 64L }, new long[] { 128L }, new long[] { 3L }, new long[] { 1L } }); // f32[1,32,384,64]
        var v47 = new Call(new Unary(UnaryOp.Neg), new Expr[] { v46 }); // f32[1,32,384,64]
        var v48 = new Call(new Slice(), new Expr[] { v44, new long[] { 0L }, new long[] { 64L }, new long[] { 3L }, new long[] { 1L } }); // f32[1,32,384,64]
        var v49 = new IR.Tuple(new Expr[] { v47, v48 }); // (f32[1,32,384,64], f32[1,32,384,64])

        var v50 = new Call(new Concat(axis: 3), new BaseExpr[] { v49 }); // f32[1,32,384,128]
        var v51 = new Call(new Binary(BinaryOp.Mul), new Expr[] { v50, v35 }); // f32[1,32,384,128]
        var v52 = new Call(new Binary(BinaryOp.Add), new Expr[] { v45, v51 }); // f32[1,32,384,128]
        var v53 = new IR.Tuple(new Expr[] { v17, v14, v42, v39, v52, v35, v3, v26 }); // (f32[1,32,384,128], f32[1,8,384,128], f32[1,32,384,128], f32[1,8,384,128], f32[1,32,384,128], f32[1,1,384,128], f32[1,384,4096], f32[1,1,384,128])

        var main = new Function("llama8B", v53, new[] { v2, v21, v1, v18 });

        var input_tensor1 = IR.F.Random.Normal(DataTypes.Int64, 0, 0, 2, new[] { 1, 384 }).Evaluate().AsTensor();
        var input_tensor2 = IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1, 384, 64 }).Evaluate().AsTensor();
        var input_tensor3 = IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1, 8, 0, 128 }).Evaluate().AsTensor();
        var input_tensor4 = IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 1, 8, 0, 128 }).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { v2, Value.FromTensor(input_tensor1) },
                { v21, Value.FromTensor(input_tensor2) },
                { v1, Value.FromTensor(input_tensor3) },
                { v18, Value.FromTensor(input_tensor4) },
            };
        var outputs = main.Body.Evaluate(feedDict).AsTensors();

        Add(main, main.Name, new[] { input_tensor1, input_tensor2, input_tensor3, input_tensor4 }.Concat(outputs).ToArray());
    }
}

internal sealed class TilingCaseLLaMaMHA : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseLLaMaMHA()
    {
        var var_5 = new Var("var_5", new TensorType(DataTypes.Float32, new[] { 1, 384, 4096 }));
        var var_6 = new Var("var_6", new TensorType(DataTypes.Float32, new[] { 1, 384, 128 }));
        var var_7 = new Var("var_7", new TensorType(DataTypes.Float32, new[] { 1, 384, 128 }));
        var var_8 = new Var("var_8", new TensorType(DataTypes.Float32, new[] { 1, 1, 384, 384 }));

        var v0 = IR.F.NN.LayerNorm(2, 1E-06f, var_5, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }).Evaluate().AsTensor(), false);
        var v1 = Unsqueeze(v0, new[] { 1 });
        var v2 = IR.F.Math.MatMul(v1, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 32, 4096, 128 }).Evaluate().AsTensor());
        var v3 = Reshape(var_6, new[] { 1, 1, 384, 128 });
        var v4 = IR.F.Math.Binary(BinaryOp.Mul, v2, v3);
        var v5 = Slice(v2, new[] { 64L }, new[] { 9223372036854775807L }, new[] { 3L }, new[] { 1L });
        var v6 = IR.F.Math.Unary(UnaryOp.Neg, v5);
        var v7 = Slice(v2, new[] { 0L }, new[] { 64L }, new[] { 3L }, new[] { 1L });
        var v8 = new IR.Tuple(v6, v7);
        var v9 = Concat(v8, 3);
        var v10 = Reshape(var_7, new[] { 1, 1, 384, 128 });
        var v11 = IR.F.Math.Binary(BinaryOp.Mul, v9, v10);
        var v12 = IR.F.Math.Binary(BinaryOp.Add, v4, v11);
        var v13 = Unsqueeze(v0, new[] { 1 });
        var v14 = IR.F.Math.MatMul(v13, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 32, 4096, 128 }).Evaluate().AsTensor());
        var v15 = IR.F.Math.Binary(BinaryOp.Mul, v14, v3);
        var v16 = Slice(v14, new[] { 64L }, new[] { 9223372036854775807L }, new[] { 3L }, new[] { 1L });
        var v17 = IR.F.Math.Unary(UnaryOp.Neg, v16);
        var v18 = Slice(v14, new[] { 0L }, new[] { 64L }, new[] { 3L }, new[] { 1L });
        var v19 = new IR.Tuple(v17, v18);
        var v20 = Concat(v19, 3);
        var v21 = IR.F.Math.Binary(BinaryOp.Mul, v20, v10);
        var v22 = IR.F.Math.Binary(BinaryOp.Add, v15, v21);
        var v23 = Transpose(v22, new[] { 0L, 1L, 3L, 2L });
        var v24 = IR.F.Math.MatMul(v12, v23);
        var v25 = IR.F.Math.Binary(BinaryOp.Div, v24, new[] { 11.313708f });
        var v26 = IR.F.Math.Binary(BinaryOp.Add, v25, var_8);
        var v27 = IR.F.NN.Softmax(v26, 3);
        var v28 = Unsqueeze(v0, new[] { 1 });
        var v29 = IR.F.Math.MatMul(v28, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 32, 4096, 128 }).Evaluate().AsTensor());
        var v30 = IR.F.Math.MatMul(v27, v29);
        var v31 = Transpose(v30, new[] { 0L, 2L, 1L, 3L });
        var v32 = Reshape(v31, new[] { 1L, 384L, 4096L });
        var v33 = IR.F.Math.MatMul(v32, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096, 4096 }).Evaluate().AsTensor());
        var v34 = IR.F.Math.Binary(BinaryOp.Add, var_5, v33);
        var v35 = IR.F.NN.LayerNorm(2, 1E-06f, v34, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096 }).Evaluate().AsTensor(), false);
        var v36 = IR.F.Math.MatMul(v35, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096, 11008 }).Evaluate().AsTensor());
        var v37 = IR.F.NN.Swish(v36, 1f);
        var v38 = IR.F.Math.MatMul(v35, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 4096, 11008 }).Evaluate().AsTensor());
        var v39 = IR.F.Math.Binary(BinaryOp.Mul, v37, v38);
        var v40 = IR.F.Math.MatMul(v39, IR.F.Random.Normal(DataTypes.Float32, 0, 0.1, 2, new[] { 11008, 4096 }).Evaluate().AsTensor());
        var v41 = IR.F.Math.Binary(BinaryOp.Add, v34, v40);

        var main = new Function("llama_mha", new IR.Tuple(v41), new[] { var_5, var_6, var_7, var_8 });

        var input_tensor1 = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 384, 4096 }).Evaluate().AsTensor();
        var input_tensor2 = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 384, 128 }).Evaluate().AsTensor();
        var input_tensor3 = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 384, 128 }).Evaluate().AsTensor();
        var input_tensor4 = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 1, 384, 384 }).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { var_5, Value.FromTensor(input_tensor1) },
                { var_6, Value.FromTensor(input_tensor2) },
                { var_7, Value.FromTensor(input_tensor3) },
                { var_8, Value.FromTensor(input_tensor4) },
            };
        var outputs = main.Body.Evaluate(feedDict).AsTensors();

        Add(main, main.Name, new[] { input_tensor1, input_tensor2, input_tensor3, input_tensor4 }.Concat(outputs).ToArray());
    }
}

internal sealed class TilingCaseVAEDecRes : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseVAEDecRes()
    {
        var var_6 = new Var("var_6", new TensorType(DataTypes.Float32, new[] { 1, 4, 64, 64 }));

        var v0 = IR.F.NN.Conv2D(var_6, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 4, 4, 1, 1 }).Evaluate().AsTensor(), new[] { 0f, 0f, 0f, 0f }, new[] { 1L, 1L }, new[,] { { 0L, 0L }, { 0L, 0L } }, new[] { 1L, 1L }, PadMode.Constant, 1, new[] { float.NegativeInfinity, float.PositiveInfinity });
#pragma warning disable SA1001 // Commas should be spaced correctly
        var v1 = IR.F.Math.Binary(BinaryOp.Add, v0, new[, ,] { { { -0.025753183f } }, { { -0.1013499f } }, { { -0.21367496f } }, { { 0.18700215f } } });
#pragma warning restore SA1001 // Commas should be spaced correctly
        var v2 = IR.F.Math.Clamp(v1, float.NegativeInfinity, float.PositiveInfinity);
        var v3 = IR.F.NN.Conv2D(v2, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 4, 3, 3 }).Evaluate().AsTensor(), Tensor.Zeros<float>(new long[] { 512 }), new[] { 1L, 1L }, new[,] { { 1L, 1L }, { 1L, 1L } }, new[] { 1L, 1L }, PadMode.Constant, 1, new[] { float.NegativeInfinity, float.PositiveInfinity });
        var v4 = IR.F.Math.Binary(BinaryOp.Add, v3, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v5 = IR.F.Math.Clamp(v4, float.NegativeInfinity, float.PositiveInfinity);
        var v6 = Reshape(v5, new[] { 1L, 32L, 65536L });
        var v7 = IR.F.NN.InstanceNormalization(v6, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 32 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new[] { 32 }).Evaluate().AsTensor(), 1E-06f);
        var v8 = Reshape(v7, new[] { 1L, 512L, 64L, 64L });
        var v9 = IR.F.Math.Binary(BinaryOp.Mul, v8, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v10 = IR.F.Math.Binary(BinaryOp.Add, v9, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v11 = IR.F.NN.Swish(v10, 1f);
        var v12 = IR.F.NN.Conv2D(v11, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 512, 3, 3 }).Evaluate().AsTensor(), Tensor.Zeros<float>(new long[] { 512 }), new[] { 1L, 1L }, new[,] { { 1L, 1L }, { 1L, 1L } }, new[] { 1L, 1L }, PadMode.Constant, 1, new[] { float.NegativeInfinity, float.PositiveInfinity });
        var v13 = IR.F.Math.Binary(BinaryOp.Add, v12, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v14 = IR.F.Math.Clamp(v13, float.NegativeInfinity, float.PositiveInfinity);
        var v15 = Reshape(v14, new[] { 1L, 32L, 65536L });
        var v16 = IR.F.NN.InstanceNormalization(v15, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 32 }).Evaluate().AsTensor(), IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new[] { 32 }).Evaluate().AsTensor(), 1E-06f);
        var v17 = Reshape(v16, new[] { 1L, 512L, 64L, 64L });
        var v18 = IR.F.Math.Binary(BinaryOp.Mul, v17, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v19 = IR.F.Math.Binary(BinaryOp.Add, v18, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v20 = IR.F.NN.Swish(v19, 1f);
        var v21 = IR.F.NN.Conv2D(v20, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 512, 3, 3 }).Evaluate().AsTensor(), Tensor.Zeros<float>(new long[] { 512 }), new[] { 1L, 1L }, new[,] { { 1L, 1L }, { 1L, 1L } }, new[] { 1L, 1L }, PadMode.Constant, 1, new[] { float.NegativeInfinity, float.PositiveInfinity });
        var v22 = IR.F.Math.Binary(BinaryOp.Add, v21, IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, new long[] { 512, 1, 1 }).Evaluate().AsTensor());
        var v23 = IR.F.Math.Clamp(v22, float.NegativeInfinity, float.PositiveInfinity);
        var v24 = IR.F.Math.Binary(BinaryOp.Add, v5, v23);

        var main = new Function("vae_dec_res", new IR.Tuple(v24), new[] { var_6 });

        var input_tensor1 = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 4, 64, 64 }).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { var_6, Value.FromTensor(input_tensor1) },
            };
        var outputs = main.Body.Evaluate(feedDict).AsTensors();

        Add(main, main.Name, new[] { input_tensor1 }.Concat(outputs).ToArray());
    }
}

internal sealed class TilingCaseExpand : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseExpand()
    {
        var shape = new long[] { 1, 32, 1 };
        var input = new Var("input", new TensorType(DataTypes.Int64, shape));
        var newShape = new RankedShape(2, 32, 32);

        var v0 = new Call(new IR.Tensors.Expand(), input, newShape);
        var main = new Function("expand", v0, new[] { input });

        var input_tensor = IR.F.Random.Uniform(DataTypes.Int64, 100, 1, 2, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { input, Value.FromTensor(input_tensor) },
            };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseClamp : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseClamp()
    {
        var shape = new[] { 1, 32, 32 };
        var input = new Var("input", new TensorType(DataTypes.Float32, shape));

        var v0 = new Call(new IR.Math.Clamp(), input, (Expr)0f, (Expr)6f);
        var main = new Function("clamp", v0, new[] { input });

        var input_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, shape).Evaluate().AsTensor();
        var feedDict = new Dictionary<IVar, IValue>
            {
                { input, Value.FromTensor(input_tensor) },
            };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal abstract class TilingCaseBinaryUnary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseBinaryUnary()
    {
        var lhsShape = LhsShape;
        var rhsShape = RhsShape;
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhs = new Var("rhs", new TensorType(DataTypes.Float32, rhsShape));

        var v0 = new Call(new IR.Math.Binary(BinaryOp.Mul), lhs, rhs);
        var v1 = new Call(new IR.Math.Unary(UnaryOp.Abs), v0);
        var main = new Function("binary_unary", v1, new[] { lhs, rhs });

        var lhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, lhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(lhs_tensor.BytesBuffer);
        }

        var rhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, rhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_1.bin"))
        {
            fs.Write(rhs_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
            { rhs, Value.FromTensor(rhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, rhs_tensor, output });
    }

    public abstract int[] LhsShape { get; }

    public abstract int[] RhsShape { get; }
}

internal sealed class TilingCaseMatmulBinary : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseMatmulBinary()
    {
        var lhsShape = new[] { 1, 64, 384, 8192 };
        var lhs = new Var("lhs", new TensorType(DataTypes.Float32, lhsShape));
        var rhsShape = new[] { 1, 64, 8192, 128 };
        Expr rhs = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, rhsShape).Evaluate().AsTensor().Cast<float>();
        Expr bias = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, new[] { 1, 64, 1, 1 }).Evaluate().AsTensor().Cast<float>();

        var v0 = new Call(new IR.Math.MatMul(DataTypes.Float32), lhs, rhs);
        var v1 = new Call(new IR.Math.Binary(BinaryOp.Add), v0, bias);
        var main = new Function("matmul_binary", v1, new[] { lhs });

        var lhs_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, lhsShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(lhs_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { lhs, Value.FromTensor(lhs_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { lhs_tensor, output });
    }
}

internal sealed class TilingCaseWhere : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseWhere()
    {
        var shape = new[] { 1, 384, 384 };
        var cond = new Var("input", new TensorType(DataTypes.Boolean, shape));
        Expr x = Tensor.FromArray(new[] { -3.4028235E+10f });
        Expr y = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 3, shape).Evaluate().AsTensor();

        var v0 = new Call(new IR.Tensors.Where(false), cond, x, y);
        var main = new Function("where", v0, new[] { cond });

        var random = new System.Random();
        var input_1darray = Enumerable.Range(0, shape.Aggregate(1, (x, y) => x * y)).Select(_ => random.Next(2) == 1).ToArray();
        var input_3darray = new bool[1, 384, 384];
        for (int i = 0; i < input_3darray.GetLength(0); i++)
        {
            for (int j = 0; j < input_3darray.GetLength(1); j++)
            {
                for (int k = 0; k < input_3darray.GetLength(2); k++)
                {
                    input_3darray[i, j, k] = input_1darray[(i * input_3darray.GetLength(1) * input_3darray.GetLength(2)) + (j * input_3darray.GetLength(2)) + k];
                }
            }
        }

        var input_tensor = Tensor.FromArray(input_3darray);
        var feedDict = new Dictionary<IVar, IValue>
        {
            { cond, Value.FromTensor(input_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { input_tensor, output });
    }
}

internal sealed class TilingCaseScatterND : TheoryData<Function, string, Tensor[]>
{
    public TilingCaseScatterND()
    {
        var inputShape = new[] { 1, 1, 256, 256 };
        var input = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 0, inputShape).Evaluate().AsTensor().Cast<float>();
        var indicesShape = new[] { 1, 1, 256, 256, 4 };
        var indices = IR.F.Random.Uniform(DataTypes.Int64, 0, 1, 0, indicesShape).Evaluate().AsTensor().Cast<float>();
        var updatesShape = new[] { 1, 1, 256, 256 };
        var updates = new Var("updates", new TensorType(DataTypes.Float32, updatesShape));

        var main = new Function("scatter_nd", IR.F.Tensors.ScatterND(input, indices, updates), new[] { updates });

        var updates_tensor = IR.F.Random.Normal(DataTypes.Float32, 0, 1, 2, updatesShape).Evaluate().AsTensor();
        using (var fs = Diagnostics.DumpScope.Current.OpenFile("input_0.bin"))
        {
            fs.Write(updates_tensor.BytesBuffer);
        }

        var feedDict = new Dictionary<IVar, IValue>
        {
            { updates, Value.FromTensor(updates_tensor) },
        };
        var output = main.Body.Evaluate(feedDict).AsTensor();

        Add(main, main.Name, new[] { updates_tensor, output });
    }
}
