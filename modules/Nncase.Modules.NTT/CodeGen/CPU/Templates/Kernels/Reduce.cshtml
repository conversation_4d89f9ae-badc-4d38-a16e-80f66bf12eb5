@model Nncase.CodeGen.NTT.TypedKernelTemplateModel<Nncase.TIR.NTT.Reduce>
@(Model.Indent)if (@Html.Raw(Model.Arguments[2].Symbol.Name)) {
@(Model.Indent)  reduce_@(Model.Target.ReduceOp.ToC())<true>(@Html.Raw(Model.Arguments[0].Symbol.Name), @Html.Raw(Model.Arguments[1].Symbol.Name), fixed_shape_v<@(string.Join(",", Model.Target.Axes))>, fixed_shape_v<@(string.Join(",", Model.Target.VectorizedAxes))>, fixed_shape_v<@(string.Join(",", Model.Target.PadedNums))>);
@(Model.Indent)} else {
@(Model.Indent)  reduce_@(Model.Target.ReduceOp.ToC())<false>(@Html.Raw(Model.Arguments[0].Symbol.Name), @Html.Raw(Model.Arguments[1].Symbol.Name), fixed_shape_v<@(string.Join(",", Model.Target.Axes))>, fixed_shape_v<@(string.Join(",", Model.Target.VectorizedAxes))>, fixed_shape_v<@(string.Join(",", Model.Target.PadedNums))>);
@(Model.Indent)}

