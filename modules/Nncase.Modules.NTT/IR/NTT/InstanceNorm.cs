﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using Nncase.PatternMatch;
using static Nncase.IR.TypePatternUtility;

namespace Nncase.IR.NTT;

[PatternFunctionalGenerator]
public sealed partial class InstacneNorm : Op
{
    /// <summary>
    /// Gets input.
    /// </summary>
    public static readonly ParameterInfo Input = new(typeof(InstacneNorm), 0, "input", ParameterKind.Input);

    /// <summary>
    /// Gets scale.
    /// </summary>
    public static readonly ParameterInfo Scale = new(typeof(InstacneNorm), 1, "scale", ParameterKind.Input);

    /// <summary>
    /// Gets bias.
    /// </summary>
    public static readonly ParameterInfo Bias = new(typeof(InstacneNorm), 2, "bias", ParameterKind.Input);

    public static readonly ParameterInfo PadedNums = new(typeof(InstacneNorm), 3, "padedNums", IsShapeType());

    public float Epsilon { get; }

    public IRArray<int> VectorizedAxes { get; }

    public override string DisplayProperty() => $"Epsilon: {Epsilon}, VectorizedAxes: {VectorizedAxes}, PadedNums: {PadedNums}";
}
