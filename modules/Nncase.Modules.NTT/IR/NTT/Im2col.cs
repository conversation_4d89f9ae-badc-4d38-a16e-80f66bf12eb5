﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Nncase.IR.Math;
using Nncase.PatternMatch;

namespace Nncase.IR.NTT;

[PatternFunctionalGenerator]
public sealed partial class Im2col : Op
{
    /// <summary>
    /// Gets input.
    /// </summary>
    public static readonly ParameterInfo Input = new(typeof(Im2col), 0, "input", ParameterKind.Input);

    public IRArray<long> Kernel { get; }

    public IRArray<int> Stride { get; }

    public IRArray<int> Padding { get; }

    public IRArray<int> VectorizedAxes { get; }

    public IRArray<int> PadedNums { get; }
}
