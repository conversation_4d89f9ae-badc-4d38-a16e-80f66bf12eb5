﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.
/* This file is generated by tools/stackvm_gen/CApiGen at 12/20/2024 3:41:05 PM +08:00. */

using System;
using System.Collections.Generic;
using System.CommandLine;
using System.CommandLine.Binding;
using System.CommandLine.Invocation;
using System.Linq;
using System.Runtime.InteropServices;
using System.Text;
using System.Threading.Tasks;
using Nncase;
using Nncase.CommandLine;
using Nncase.IR;

namespace Nncase.Targets;

public sealed class NTTTargetOptionsCommand : Command
{
    public NTTTargetOptionsCommand(string name)
        : base(name)
    {
        ModelNameOption = new Option<string>(
            name: "--model-name",
            description: "the input model name.",
            getDefaultValue: () => string.Empty);
        Add(ModelNameOption);
        VectorizeOption = new Option<bool>(
            name: "--vectorize",
            description: "enable simd layout optimization.",
            getDefaultValue: () => false);
        Add(VectorizeOption);
        UnifiedMemoryArchOption = new Option<bool>(
            name: "--unified-memory-arch",
            description: "whether Unified Memory Architecture. see https://en.wikipedia.org/wiki/Unified_Memory_Access.",
            getDefaultValue: () => true);
        Add(UnifiedMemoryArchOption);
        MemoryAccessArchOption = new Option<MemoryAccessArchitecture>(
            name: "--memory-access-arch",
            description: "Memory Access Architecture.",
            getDefaultValue: () => MemoryAccessArchitecture.UMA);
        Add(MemoryAccessArchOption);
        NocArchOption = new Option<NocArchitecture>(
            name: "--noc-arch",
            description: "Noc Architecture.",
            getDefaultValue: () => NocArchitecture.Mesh);
        Add(NocArchOption);
        HierarchyKindOption = new Option<HierarchyKind>(
            name: "--hierarchy-kind",
            description: "Hierarchy Kind.",
            getDefaultValue: () => HierarchyKind.Parallel);
        Add(HierarchyKindOption);
        HierarchiesOption = new Option<IEnumerable<int[]>>(
            name: "--hierarchies",
            description: "the distributed hierarchies of hardware. eg. `8,4 4,8` for dynamic cluster search or `4` for fixed hardware.",
            parseArgument: ArgumentParsers.ParseNestedIntArray)
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(HierarchiesOption);
        HierarchyNamesOption = new Option<string>(
            name: "--hierarchy-names",
            description: "the name identify of hierarchies.",
            getDefaultValue: () => "b");
        Add(HierarchyNamesOption);
        HierarchySizesOption = new Option<IEnumerable<long>>(
            name: "--hierarchy-sizes",
            description: "the memory capacity of hierarchies.",
            getDefaultValue: () => new long[] { 1099511627776 })
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(HierarchySizesOption);
        HierarchyLatenciesOption = new Option<IEnumerable<int>>(
            name: "--hierarchy-latencies",
            description: "the latency of hierarchies.",
            getDefaultValue: () => new int[] { 10000 })
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(HierarchyLatenciesOption);
        HierarchyBandWidthsOption = new Option<IEnumerable<int>>(
            name: "--hierarchy-bandwiths",
            description: "the bandwidth of hierarchies.",
            getDefaultValue: () => new int[] { 1 })
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(HierarchyBandWidthsOption);
        MemoryCapacitiesOption = new Option<IEnumerable<int>>(
            name: "--memory-capacities",
            description: "the memory capacity of single core. eg. `32 64` for sram,main",
            getDefaultValue: () => new int[] { 524288, 2147483647 })
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(MemoryCapacitiesOption);
        MemoryBandWidthsOption = new Option<IEnumerable<int>>(
            name: "--memory-bandwidths",
            description: "the memory bandwidth of single core. eg. `64 8` for sram,main",
            getDefaultValue: () => new int[] { 64, 8 })
        {
            AllowMultipleArgumentsPerToken = true,
        };
        Add(MemoryBandWidthsOption);
        DistributedSchemeOption = new Option<string>(
            name: "--distributed--scheme",
            description: "the distributed scheme path.",
            getDefaultValue: () => string.Empty);
        Add(DistributedSchemeOption);
        CustomOpSchemeOption = new Option<string>(
            name: "--custom-op-scheme",
            description: "the custom-op scheme path.",
            getDefaultValue: () => string.Empty);
        Add(CustomOpSchemeOption);
    }

    public Option<string> ModelNameOption { get; }

    public Option<bool> VectorizeOption { get; }

    public Option<bool> UnifiedMemoryArchOption { get; }

    public Option<MemoryAccessArchitecture> MemoryAccessArchOption { get; }

    public Option<NocArchitecture> NocArchOption { get; }

    public Option<HierarchyKind> HierarchyKindOption { get; }

    public Option<IEnumerable<int[]>> HierarchiesOption { get; }

    public Option<string> HierarchyNamesOption { get; }

    public Option<IEnumerable<long>> HierarchySizesOption { get; }

    public Option<IEnumerable<int>> HierarchyLatenciesOption { get; }

    public Option<IEnumerable<int>> HierarchyBandWidthsOption { get; }

    public Option<IEnumerable<int>> MemoryCapacitiesOption { get; }

    public Option<IEnumerable<int>> MemoryBandWidthsOption { get; }

    public Option<string> DistributedSchemeOption { get; }

    public Option<string> CustomOpSchemeOption { get; }
}

public sealed class NTTTargetOptionsBinder
{
    private readonly NTTTargetOptionsCommand _cmd;

    public NTTTargetOptionsBinder(NTTTargetOptionsCommand cmd)
    {
        _cmd = cmd;
    }

    public NTTTargetOptions GetBoundValue(InvocationContext context)
    {
        return new NTTTargetOptions()
        {
            ModelName = context.ParseResult.GetValueForOption(_cmd.ModelNameOption)!,
            Vectorize = context.ParseResult.GetValueForOption(_cmd.VectorizeOption)!,
            UnifiedMemoryArch = context.ParseResult.GetValueForOption(_cmd.UnifiedMemoryArchOption)!,
            MemoryAccessArch = context.ParseResult.GetValueForOption(_cmd.MemoryAccessArchOption)!,
            NocArch = context.ParseResult.GetValueForOption(_cmd.NocArchOption)!,
            HierarchyKind = context.ParseResult.GetValueForOption(_cmd.HierarchyKindOption)!,
            Hierarchies = context.ParseResult.GetValueForOption(_cmd.HierarchiesOption)!.ToArray(),
            HierarchyNames = context.ParseResult.GetValueForOption(_cmd.HierarchyNamesOption)!,
            HierarchySizes = context.ParseResult.GetValueForOption(_cmd.HierarchySizesOption)!.ToArray(),
            HierarchyLatencies = context.ParseResult.GetValueForOption(_cmd.HierarchyLatenciesOption)!.ToArray(),
            HierarchyBandWidths = context.ParseResult.GetValueForOption(_cmd.HierarchyBandWidthsOption)!.ToArray(),
            MemoryCapacities = context.ParseResult.GetValueForOption(_cmd.MemoryCapacitiesOption)!.ToArray(),
            MemoryBandWidths = context.ParseResult.GetValueForOption(_cmd.MemoryBandWidthsOption)!.ToArray(),
            DistributedScheme = context.ParseResult.GetValueForOption(_cmd.DistributedSchemeOption)!,
            CustomOpScheme = context.ParseResult.GetValueForOption(_cmd.CustomOpSchemeOption)!,
        };
    }
}
