﻿// Copyright (c) Canaan Inc. All rights reserved.
// Licensed under the Apache license. See LICENSE file in the project root for full license information.

using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using DryIoc.ImTools;
using NetFabric.Hyperlinq;
using Nncase.IR;
using Nncase.IR.Math;
using Nncase.IR.NN;
using Nncase.IR.Shapes;
using Nncase.IR.Tensors;
using Nncase.PatternMatch;
using Nncase.Utilities;

using static Nncase.IR.TypePatternUtility;
using static Nncase.PatternMatch.F.Math;
using static Nncase.PatternMatch.F.Tensors;
using static Nncase.PatternMatch.Utility;

namespace Nncase.Passes.Rules.NTT;

[RuleGenerator]
public sealed partial class VectorizeWherePropagation : RewriteRule<Pattern>
{
    public VectorizeWherePropagation(MaskVectorStyle maskVectorStyle)
    {
        MaskVectorStyle = maskVectorStyle;
    }

    public MaskVectorStyle MaskVectorStyle { get; }

    public override Pattern Pattern { get; } =
        PatternMatch.F.Tensors.IsPack(
            "vectorize",
            "caller",
            _ => true,
            IsWhere(
                "where",
                "callee",
                _ => true,
                IsWildcard("cond"),
                IsWildcard("lhs"),
                IsWildcard("rhs")));

    private Expr? GetReplace(Pack vectorize, Call callee, Expr cond, Expr lhs, Expr rhs)
    {
        var condShape = cond.CheckedShape;
        var lhsShape = lhs.CheckedShape;
        var rhsShape = rhs.CheckedShape;
        var outputRank = callee.CheckedShape.Rank;

        var condVectorizedAxes = new List<int>();
        var lhsVectorizedAxes = new List<int>();
        var rhsVectorizedAxes = new List<int>();
        var condLanes = new List<int>();
        var lhsLanes = new List<int>();
        var rhsLanes = new List<int>();

        for (int i = 0; i < vectorize.Axes.Count; i++)
        {
            var axis = vectorize.Axes[i];
            var lanes = vectorize.Lanes[i];

            if (!VectorizeUtility.TryPropagateArgument(outputRank, condShape, axis, lanes, condVectorizedAxes, condLanes))
            {
                return null; // Cannot vectorize cond.
            }

            if (!VectorizeUtility.TryPropagateArgument(outputRank, lhsShape, axis, lanes, lhsVectorizedAxes, lhsLanes))
            {
                return null; // Cannot vectorize lhs.
            }

            if (!VectorizeUtility.TryPropagateArgument(outputRank, rhsShape, axis, lanes, rhsVectorizedAxes, rhsLanes))
            {
                return null; // Cannot vectorize rhs.
            }
        }

        if (condVectorizedAxes.Count > 1)
        {
            return null; // Mask can only be vectorized along one axis.
        }

        var maskElementBits = lhs.CheckedDataType.SizeInBytes * 8;
        return callee.WithArguments([
            (Where.Cond, condVectorizedAxes.Count == 0 ? cond : IR.F.Tensors.VectorizeMask(cond, MaskVectorStyle, maskElementBits, condLanes[0], condVectorizedAxes[0])),
            (Where.X, IR.F.Tensors.Pack(lhs, lhsLanes.ToArray(), lhsVectorizedAxes.ToArray())),
            (Where.Y, IR.F.Tensors.Pack(rhs, rhsLanes.ToArray(), rhsVectorizedAxes.ToArray())),
        ]);
    }
}
