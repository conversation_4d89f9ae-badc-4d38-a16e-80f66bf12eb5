/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#include "pytype_utils.h"
#include "runtime_tensor.h"
#include "type_casters.h"
#include <llm_ffi.h>
#include <nncase/compiler.h>
#include <nncase/llm/paged_attention_kv_cache.h>
#include <nncase/runtime/interpreter.h>
#include <nncase/runtime/runtime_op_utility.h>
#include <nncase/version.h>
#include <pybind11/iostream.h>
#include <pybind11/numpy.h>
#include <pybind11/pybind11.h>
#include <pybind11/stl.h>
#include <pybind11/stl_bind.h>

namespace py = pybind11;
using namespace nncase;
using namespace nncase::clr;
using namespace nncase::runtime;

namespace {} // namespace

namespace pybind11::detail {
std::atomic_bool g_python_shutdown = false;
}

PYBIND11_MODULE(_nncase, m) {
    m.doc() = "nncase Library";
    m.attr("__version__") = NNCASE_VERSION NNCASE_VERSION_SUFFIX;

    m.add_object("_cleanup", py::capsule([]() {
                     nncase_clr_uninitialize();
                     pybind11::detail::g_python_shutdown.store(
                         true, std::memory_order_release);
                 }));
    m.def("initialize", nncase_clr_initialize);
    m.def("launch_debugger", []() { nncase_clr_api()->luanch_debugger(); });

    py::enum_<nncase_model_quant_mode_t>(m, "ModelQuantMode")
        .value("NoQuant", nncase_mqm_no_quant)
        .value("UsePTQ", nncase_mqm_use_ptq)
        .value("UseQAT", nncase_mqm_use_qat);

    py::enum_<nncase_dump_flags_t>(m, "DumpFlags", py::arithmetic())
        .value("Nothing", nncase_dump_flags_none)
        .value("ImportOps", nncase_dump_flags_import_ops)
        .value("PassIR", nncase_dump_flags_pass_ir)
        .value("EGraphCost", nncase_dump_flags_egraph_cost)
        .value("Rewrite", nncase_dump_flags_rewrite)
        .value("Calibration", nncase_dump_flags_calibration)
        .value("Evaluator", nncase_dump_flags_evaluator)
        .value("Compile", nncase_dump_flags_compile)
        .value("Tiling", nncase_dump_flags_tiling)
        .value("Schedule", nncase_dump_flags_schedule)
        .value("CodeGen", nncase_dump_flags_codegen);

    py::enum_<nncase_calib_method_t>(m, "CalibMethod")
        .value("NoClip", nncase_calib_noclip)
        .value("Kld", nncase_calib_kld);

    py::enum_<nncase_quant_type_t>(m, "QuantType")
        .value("Uint8", nncase_qt_uint8)
        .value("Int8", nncase_qt_int8)
        .value("Int16", nncase_qt_int16);

    py::enum_<nncase_input_type_t>(m, "InputType")
        .value("Uint8", nncase_it_uint8)
        .value("Int8", nncase_it_int8)
        .value("Float32", nncase_it_float32);

    py::enum_<nncase_finetune_weights_method_t>(m, "FineTuneWeightsMethod")
        .value("NoFineTuneWeights", nncase_no_finetune_weights)
        .value("UseSquant", nncase_finetune_weights_squant)
        .value("UseAdaRound", nncase_finetune_weights_adaround);

    py::class_<import_options>(m, "ImportOptions")
        .def(py::init())
        .def_property("huggingface_options",
                      py::overload_cast<>(&import_options::huggingface_options),
                      py::overload_cast<const huggingface_options &>(
                          &import_options::huggingface_options));

    py::enum_<huggingface_attenion_backend>(m, "HuggingFaceAttentionBackend")
        .value("Default", huggingface_attenion_backend::_default)
        .value("PagedAttention", huggingface_attenion_backend::paged_attention);

    py::class_<huggingface_options>(m, "HuggingFaceOptions")
        .def(py::init())
        .def_property(
            "output_logits",
            py::overload_cast<>(&huggingface_options::output_logits),
            py::overload_cast<bool>(&huggingface_options::output_logits))
        .def_property(
            "output_hidden_states",
            py::overload_cast<>(&huggingface_options::output_hidden_states),
            py::overload_cast<bool>(&huggingface_options::output_hidden_states))
        .def_property(
            "num_layers", py::overload_cast<>(&huggingface_options::num_layers),
            py::overload_cast<int32_t>(&huggingface_options::num_layers))
        .def_property(
            "attention_backend",
            py::overload_cast<>(&huggingface_options::attention_backend),
            py::overload_cast<huggingface_attenion_backend>(
                &huggingface_options::attention_backend))
        .def_property("config", nullptr,
                      py::overload_cast<const llm::paged_attention_config &>(
                          &huggingface_options::config))
        .def_property(
            "max_model_len",
            py::overload_cast<>(&huggingface_options::max_model_len),
            py::overload_cast<int32_t>(&huggingface_options::max_model_len));

    py::class_<compile_options>(m, "CompileOptions")
        .def(py::init())
        .def_property(
            "input_format", py::overload_cast<>(&compile_options::input_format),
            py::overload_cast<std::string_view>(&compile_options::input_format))
        .def_property(
            "dump_dir", py::overload_cast<>(&compile_options::dump_dir),
            py::overload_cast<std::string_view>(&compile_options::dump_dir))
        .def_property("dump_flags",
                      py::overload_cast<>(&compile_options::dump_flags),
                      py::overload_cast<nncase_dump_flags_t>(
                          &compile_options::dump_flags))
        .def_property("quantize_options",
                      py::overload_cast<>(&compile_options::quantize_options),
                      py::overload_cast<const quantize_options &>(
                          &compile_options::quantize_options))
        .def_property("preprocess",
                      py::overload_cast<>(&compile_options::preprocess),
                      py::overload_cast<bool>(&compile_options::preprocess))
        .def_property(
            "input_layout", py::overload_cast<>(&compile_options::input_layout),
            py::overload_cast<std::string_view>(&compile_options::input_layout))
        .def_property("output_layout",
                      py::overload_cast<>(&compile_options::output_layout),
                      py::overload_cast<std::string_view>(
                          &compile_options::output_layout))
        .def_property(
            "input_file", py::overload_cast<>(&compile_options::input_file),
            py::overload_cast<std::string_view>(&compile_options::input_file))
        .def_property("input_type",
                      py::overload_cast<>(&compile_options::input_type),
                      py::overload_cast<nncase_input_type_t>(
                          &compile_options::input_type))
        .def_property(
            "input_shape", py::overload_cast<>(&compile_options::input_shape),
            py::overload_cast<std::string_view>(&compile_options::input_shape))
        .def_property(
            "input_range", py::overload_cast<>(&compile_options::input_range),
            py::overload_cast<std::string_view>(&compile_options::input_range))
        .def_property("swapRB", py::overload_cast<>(&compile_options::swapRB),
                      py::overload_cast<bool>(&compile_options::swapRB))
        .def_property(
            "letterbox_value",
            py::overload_cast<>(&compile_options::letterbox_value),
            py::overload_cast<float>(&compile_options::letterbox_value))
        .def_property(
            "mean", py::overload_cast<>(&compile_options::mean),
            py::overload_cast<std::string_view>(&compile_options::mean))
        .def_property(
            "std", py::overload_cast<>(&compile_options::std),
            py::overload_cast<std::string_view>(&compile_options::std))
        .def_property(
            "shape_bucket_options",
            py::overload_cast<>(&compile_options::shape_bucket_options),
            py::overload_cast<const shape_bucket_options &>(
                &compile_options::shape_bucket_options))
        .def(
            "set_cpu_target_options",
            [](compile_options &obj, const cpu_target_options &target_options) {
                nncase_clr_api()->compile_options_set_cpu_target_options(
                    obj.get(), target_options.get());
            });

    py::class_<target>(m, "Target")
        .def(py::init<std::string_view>())
        .def_static("exists", &target::exists);

    py::class_<quantize_options>(m, "QuantizeOptions")
        .def(py::init())
        .def_property(
            "calibration_dataset",
            py::overload_cast<>(&quantize_options::calibration_dataset),
            py::overload_cast<const calibration_dataset_provider &>(
                &quantize_options::calibration_dataset))
        .def_property("model_quant_mode",
                      py::overload_cast<>(&quantize_options::model_quant_mode),
                      py::overload_cast<nncase_model_quant_mode_t>(
                          &quantize_options::model_quant_mode))
        .def_property("calibrate_method",
                      py::overload_cast<>(&quantize_options::calibrate_method),
                      py::overload_cast<nncase_calib_method_t>(
                          &quantize_options::calibrate_method))
        .def_property("quant_type",
                      py::overload_cast<>(&quantize_options::quant_type),
                      py::overload_cast<nncase_quant_type_t>(
                          &quantize_options::quant_type))
        .def_property("w_quant_type",
                      py::overload_cast<>(&quantize_options::w_quant_type),
                      py::overload_cast<nncase_quant_type_t>(
                          &quantize_options::w_quant_type))
        .def_property(
            "finetune_weights_method",
            py::overload_cast<>(&quantize_options::finetune_weights_method),
            py::overload_cast<nncase_finetune_weights_method_t>(
                &quantize_options::finetune_weights_method))
        .def_property("use_mix_quant",
                      py::overload_cast<>(&quantize_options::use_mix_quant),
                      py::overload_cast<bool>(&quantize_options::use_mix_quant))
        .def_property("quant_scheme",
                      py::overload_cast<>(&quantize_options::quant_scheme),
                      py::overload_cast<std::string_view>(
                          &quantize_options::quant_scheme))
        .def_property(
            "quant_scheme_strict_mode",
            py::overload_cast<>(&quantize_options::quant_scheme_strict_mode),
            py::overload_cast<bool>(
                &quantize_options::quant_scheme_strict_mode))
        .def_property(
            "export_quant_scheme",
            py::overload_cast<>(&quantize_options::export_quant_scheme),
            py::overload_cast<bool>(&quantize_options::export_quant_scheme))
        .def_property("export_weight_range_by_channel",
                      py::overload_cast<>(
                          &quantize_options::export_weight_range_by_channel),
                      py::overload_cast<bool>(
                          &quantize_options::export_weight_range_by_channel))
        .def_property(
            "dump_quant_error",
            py::overload_cast<>(&quantize_options::dump_quant_error),
            py::overload_cast<bool>(&quantize_options::dump_quant_error))
        .def_property(
            "dump_quant_error_symmetric_for_signed",
            py::overload_cast<>(
                &quantize_options::dump_quant_error_symmetric_for_signed),
            py::overload_cast<bool>(
                &quantize_options::dump_quant_error_symmetric_for_signed));

    py::class_<shape_bucket_options>(m, "ShapeBucketOptions")
        .def(py::init())
        .def_property("enable",
                      py::overload_cast<>(&shape_bucket_options::enable),
                      py::overload_cast<bool>(&shape_bucket_options::enable))
        .def_property(
            "range_info",
            py::overload_cast<>(&shape_bucket_options::range_info),
            py::overload_cast<std::map<std::string, std::tuple<int, int>>>(
                &shape_bucket_options::range_info))
        .def_property(
            "segments_count",
            py::overload_cast<>(&shape_bucket_options::segments_count),
            py::overload_cast<int>(&shape_bucket_options::segments_count))
        .def_property("fix_var_map",
                      py::overload_cast<>(&shape_bucket_options::fix_var_map),
                      py::overload_cast<std::map<std::string, int>>(
                          &shape_bucket_options::fix_var_map));

    // clang-format off
    /* This block is generated by tools/stackvm_gen/CApiGen at 12/20/2024 3:41:05 PM +08:00. */

    py::enum_<memory_access_architecture_t>(m, "MemoryAccessArchitecture")
      .value("UMA", memory_access_architecture_uma)
      .value("NUMA", memory_access_architecture_numa);

    py::enum_<noc_architecture_t>(m, "NocArchitecture")
      .value("Mesh", noc_architecture_mesh)
      .value("CrossBar", noc_architecture_cross_bar);

    py::enum_<hierarchy_kind_t>(m, "HierarchyKind")
      .value("Parallel", hierarchy_kind_parallel)
      .value("SMT", hierarchy_kind_smt);


    py::class_<cpu_target_options>(m, "NTTTargetOptions")
      .def(py::init())
      .def_property(
        "ModelName",
        []() {},
        py::overload_cast<std::string_view>(&cpu_target_options::model_name))
      .def_property(
        "Vectorize",
        []() {},
        py::overload_cast<bool>(&cpu_target_options::vectorize))
      .def_property(
        "UnifiedMemoryArch",
        []() {},
        py::overload_cast<bool>(&cpu_target_options::unified_memory_arch))
      .def_property(
        "MemoryAccessArch",
        py::overload_cast<>(&cpu_target_options::memory_access_arch),
        py::overload_cast<memory_access_architecture_t>(&cpu_target_options::memory_access_arch))
      .def_property(
        "NocArch",
        py::overload_cast<>(&cpu_target_options::noc_arch),
        py::overload_cast<noc_architecture_t>(&cpu_target_options::noc_arch))
      .def_property(
        "HierarchyKind",
        py::overload_cast<>(&cpu_target_options::hierarchy_kind),
        py::overload_cast<hierarchy_kind_t>(&cpu_target_options::hierarchy_kind))
      .def_property(
        "Hierarchies",
        []() {},
        py::overload_cast<std::vector<std::vector<int>>>(&cpu_target_options::hierarchies))
      .def_property(
        "HierarchyNames",
        []() {},
        py::overload_cast<std::string_view>(&cpu_target_options::hierarchy_names))
      .def_property(
        "HierarchySizes",
        []() {},
        py::overload_cast<std::vector<int64_t>>(&cpu_target_options::hierarchy_sizes)) 
      .def_property(
        "HierarchyLatencies",
        []() {},
        py::overload_cast<std::vector<int>>(&cpu_target_options::hierarchy_latencies))
      .def_property(
        "HierarchyBandWidths",
        []() {},
        py::overload_cast<std::vector<int>>(&cpu_target_options::hierarchy_band_widths))
      .def_property(
        "MemoryCapacities",
        []() {},
        py::overload_cast<std::vector<int>>(&cpu_target_options::memory_capacities))
      .def_property(
        "MemoryBandWidths",
        []() {},
        py::overload_cast<std::vector<int>>(&cpu_target_options::memory_band_widths))
      .def_property(
        "DistributedScheme",
        []() {},
        py::overload_cast<std::string_view>(&cpu_target_options::distributed_scheme))
      .def_property(
        "CustomOpScheme",
        []() {},
        py::overload_cast<std::string_view>(&cpu_target_options::custom_op_scheme)) ;

    /* end the auto generated block by tools/stackvm_gen/CApiGen at 12/20/2024 3:41:05 PM +08:00. */
    // clang-format on

    py::class_<calibration_dataset_provider>(m, "CalibrationDatasetProvider")
        .def(py::init([](py::list dataset, size_t samples_count,
                         py::list fn_params) {
            std::vector<clr_object_handle_t> dataset_handles(dataset.size());
            std::vector<clr_object_handle_t> param_handles(fn_params.size());
            for (size_t i = 0; i < dataset_handles.size(); i++) {
                dataset_handles[i] = dataset[i].cast<rtvalue &>().get();
            }
            for (size_t i = 0; i < param_handles.size(); i++) {
                param_handles[i] = fn_params[i].cast<var &>().get();
            }

            array dataset_arr(nncase_array_rtvalue, dataset_handles.data(),
                              dataset.size());
            array fn_params_arr(nncase_array_var, param_handles.data(),
                                fn_params.size());
            return calibration_dataset_provider(std::move(dataset_arr),
                                                samples_count,
                                                std::move(fn_params_arr));
        }));

    py::class_<ivalue>(m, "IValue");

    py::class_<rtvalue, ivalue>(m, "RTValue")
        .def_static(
            "from_runtime_tensor",
            [](const runtime_tensor &tensor) { return rtvalue(tensor.impl()); })
        .def("to_runtime_tensor",
             [](const rtvalue &value) {
                 return runtime_tensor(
                     value.to_value().as<tensor>().unwrap_or_throw());
             })
        .def("to_runtime_tensors", [](const rtvalue &value) {
            auto v = value.to_value();
            if (v.is_a<tensor>()) {
                return std::vector<runtime_tensor>{
                    runtime_tensor(v.as<tensor>().unwrap_or_throw())};
            } else {
                auto t = v.as<tuple>().unwrap_or_throw();
                std::vector<runtime_tensor> tensors(t->fields().size());
                for (size_t i = 0; i < tensors.size(); i++) {
                    tensors[i] = runtime_tensor(
                        t->fields()[i].as<tensor>().unwrap_or_throw());
                }
                return tensors;
            }
        });

    py::class_<expr>(m, "Expr").def("evaluate", [](expr &expr, py::list params,
                                                   py::list inputs) {
        std::vector<clr_object_handle_t> param_handles(params.size());
        std::vector<clr_object_handle_t> input_handles(inputs.size());
        for (size_t i = 0; i < param_handles.size(); i++) {
            param_handles[i] = params[i].cast<clr::expr &>().get();
        }
        for (size_t i = 0; i < input_handles.size(); i++) {
            input_handles[i] = inputs[i].cast<clr::ivalue &>().get();
        }

        array params_arr(nncase_array_var, param_handles.data(), inputs.size());
        array inputs_arr(nncase_array_object, input_handles.data(),
                         inputs.size());
        return expr.evaluate(params_arr, inputs_arr);
    });

    py::enum_<nncase_dimension_kind_t>(m, "DimensionKind")
        .value("Fixed", nncase_dimension_kind_fixed)
        .value("Dynamic", nncase_dimension_kind_dynamic)
        .value("Unknown", nncase_dimension_kind_unknown);

    py::class_<dimension, expr>(m, "Dimension")
        .def_property_readonly("kind", &dimension::kind);

    py::class_<var, expr>(m, "Var").def("dimensions", [](var &var) {
        return var.dimensions().to_vector<dimension>();
    });

    py::class_<function, expr>(m, "Function")
        .def_property_readonly("body", &function::body)
        .def_property_readonly("parameters", [](function &function) {
            return function.parameters().to_vector<var>();
        });

    py::class_<ir_module>(m, "IRModule")
        .def_property_readonly("entry", &ir_module::entry);

    py::class_<compiler>(m, "Compiler")
        .def("import_tflite_module", &compiler::import_tflite_module)
        .def("import_onnx_module", &compiler::import_onnx_module)
        .def("import_ncnn_module", &compiler::import_ncnn_module)
        .def("import_huggingface_module", &compiler::import_huggingface_module)
        .def("compile", &compiler::compile)
        .def("gencode", &compiler::gencode);

    py::class_<compile_session>(m, "CompileSession")
        .def(py::init<const target &, const compile_options &>())
        .def_property_readonly("compiler", &compile_session::compiler);

    py::class_<interpreter>(m, "Simulator")
        .def(py::init())
        .def("load_model",
             [](interpreter &interp, std::span<const std::byte> buffer) {
                 interp.load_model(buffer, false).unwrap_or_throw();
             })
        .def("load_model",
             [](interpreter &interp, nncase::runtime::stream &stream) {
                 interp.load_model(stream).unwrap_or_throw();
             })
        .def_property_readonly("inputs_size", &interpreter::inputs_size)
        .def_property_readonly("outputs_size", &interpreter::outputs_size)
        .def("get_input_desc", &interpreter::input_desc)
        .def("get_output_desc", &interpreter::output_desc)
        .def("get_input_shape",
             [](interpreter &interp, size_t index) {
                 auto shape = interp.input_shape(index);
                 return std::vector<py::ssize_t>(shape.begin(), shape.end());
             })
        .def("get_output_shape",
             [](interpreter &interp, size_t index) {
                 auto shape = interp.output_shape(index);
                 return std::vector<py::ssize_t>(shape.begin(), shape.end());
             })
        .def("get_input_tensor",
             [](interpreter &interp, size_t index) {
                 return interp.input_tensor(index).unwrap_or_throw();
             })
        .def("set_input_tensor",
             [](interpreter &interp, size_t index, runtime_tensor tensor) {
                 return interp.input_tensor(index, tensor).unwrap_or_throw();
             })
        .def("get_output_tensor",
             [](interpreter &interp, size_t index) {
                 return interp.output_tensor(index).unwrap_or_throw();
             })
        .def("set_output_tensor",
             [](interpreter &interp, size_t index, runtime_tensor tensor) {
                 return interp.output_tensor(index, tensor).unwrap_or_throw();
             })
        .def("enable_profiling",
             [](interpreter &interp, uint8_t enable_profiling) {
                 interp.enable_profiling(enable_profiling);
             })
        .def("run",
             [](interpreter &interp) { interp.run().unwrap_or_throw(); });

    register_runtime_tensor(m);
    register_runtime_llm_ffi(m);
    register_ref_llm_ffi(m);
    // register_llm_interpreter(m);
}
