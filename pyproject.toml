[project]
name = "nncase"
dynamic = ["version"]
requires-python = ">=3.9"
authors = [{ name = "sunnycase" }, { email = "<EMAIL>" }]
maintainers = [{ name = "sunnycase" }, { email = "<EMAIL>" }]
readme = "README.md"
description = "A neural network compiler for AI accelerators"
license = { file = "LICENSE" }
classifiers = [
    "Programming Language :: C++",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Programming Language :: Python :: 3.13",
    "License :: OSI Approved :: Apache Software License",
    "Operating System :: OS Independent",
]
keywords = ["kendryte", "nn", "compiler", "k210", "k510", "k230"]
dependencies = ["numpy"]

[project.urls]
homepage = "https://github.com/kendryte/nncase"

[build-system]
requires = ["setuptools>=42", "wheel", "conan==2.6.0", "gitpython"]
build-backend = "setuptools.build_meta"

[tool.cibuildwheel]
build = ["cp39*", "cp310*", "cp311*", "cp312*", "cp313*"]
skip = "*musllinux*"
manylinux-x86_64-image = "sunnycase/manylinux_2_28_x86_64:1.1"
test-requires = "pytest"
test-command = [
  "pytest {project}/tests/other/test_cpu_options.py",
  "pytest {project}/tests/other/test_targets.py",
]

[tool.cibuildwheel.environment]
PYTHONPATH = "{project}/tests:$PYTHONPATH"

[tool.cibuildwheel.windows]
archs = ["AMD64"]
before-build = [
    "rm -f {project}/CMakeUserPresets.json",
    "pip install auditwheel==6.0.0"
]

[tool.cibuildwheel.linux]
archs = ["x86_64"]
before-build = [
  "rm -f {project}/CMakeUserPresets.json",
  "pip install https://github.com/sunnycase/auditwheel/releases/download/6.0.0/auditwheel-6.0.0-py3-none-any.whl",
  "if [ ! -d abseil-cpp ]; then git clone https://github.com/abseil/abseil-cpp.git && cd abseil-cpp && git checkout lts_2025_05_12 && mkdir build && cd build && cmake .. -DCMAKE_BUILD_TYPE=Release -DBUILD_SHARED_LIBS=ON -DCMAKE_INSTALL_PREFIX=/usr/ && make -j$(nproc) && make install; fi"
]
repair-wheel-command = "LD_LIBRARY_PATH=/usr/lib64 auditwheel repair -w {dest_dir} {wheel} --exclude libvulkan.so.1,libgomp.so.1"

[tool.cibuildwheel.linux.environment]
CC = "gcc-14"
CXX = "g++-14"

[tool.cibuildwheel.macos]
archs = ["arm64"]
before-build = [
  "rm -f {project}/CMakeUserPresets.json",
  "pip install auditwheel==6.0.0",
]

[tool.cibuildwheel.macos.environment]
MACOSX_DEPLOYMENT_TARGET = "12"
SYSTEM_VERSION_COMPAT = "0"
