{"version": 2, "dependencies": {"net8.0": {"CommunityToolkit.HighPerformance": {"type": "Direct", "requested": "[8.2.2, )", "resolved": "8.2.2", "contentHash": "+zIp8d3sbtYaRbM6hqDs4Ui/z34j7DcUmleruZlYLE4CVxXq+MO8XJyIs42vzeTYFX+k0Iq1dEbBUnQ4z/Gnrw=="}, "Fody": {"type": "Direct", "requested": "[6.8.1, )", "resolved": "6.8.1", "contentHash": "pwk2/No1kL1ft+zMT2y0MvzB6W5cpkdOTj+0+T2u7LGJMTw37qtlnHzCSCyvwRiZVoJ/V/DE78eQ/WEcutUVDw=="}, "MethodBoundaryAspect.Fody": {"type": "Direct", "requested": "[2.0.149, )", "resolved": "2.0.149", "contentHash": "sKKEdwd/ETEbp0XTM5vhdXsGiKRQbmsgPmt3mpqM/Emzu3UHqyBMyO/1JicCp+AQ5pVozjC2Nlv/0KvriVi2ZA==", "dependencies": {"Fody": "6.7.0"}}, "Microsoft.Extensions.Hosting": {"type": "Direct", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "ItYHpdqVp5/oFLT5QqbopnkKlyFG9EW/9nhM6/yfObeKt6Su0wkBio6AizgRHGNwhJuAtlE5VIjow5JOTrip6w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.Configuration.CommandLine": "8.0.0", "Microsoft.Extensions.Configuration.EnvironmentVariables": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.Configuration.UserSecrets": "8.0.0", "Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Hosting.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Logging.Console": "8.0.0", "Microsoft.Extensions.Logging.Debug": "8.0.0", "Microsoft.Extensions.Logging.EventLog": "8.0.0", "Microsoft.Extensions.Logging.EventSource": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Options": {"type": "Direct", "requested": "[8.0.2, )", "resolved": "8.0.2", "contentHash": "dWGKvhFybsaZpGmzkGCbNNwBD1rVlWzrZKANLW/CcbFJpCEceMCGzT7zZwHOGBCbwM0SzBuceMj5HN1LKV1QqA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.NET.Test.Sdk": {"type": "Direct", "requested": "[17.10.0, )", "resolved": "17.10.0", "contentHash": "0/2HeACkaHEYU3wc83YlcD2Fi4LMtECJjqrtvw0lPi9DCEa35zSPt1j4fuvM8NagjDqJuh1Ja35WcRtn1Um6/A==", "dependencies": {"Microsoft.CodeCoverage": "17.10.0", "Microsoft.TestPlatform.TestHost": "17.10.0"}}, "Microsoft.TestPlatform.ObjectModel": {"type": "Direct", "requested": "[17.10.0, )", "resolved": "17.10.0", "contentHash": "KkwhjQevuDj0aBRoPLY6OLAhGqbPUEBuKLbaCs0kUVw29qiOYncdORd4mLVJbn9vGZ7/iFGQ/+AoJl0Tu5Umdg==", "dependencies": {"System.Reflection.Metadata": "1.6.0"}}, "StyleCop.Analyzers": {"type": "Direct", "requested": "[1.2.0-beta.556, )", "resolved": "1.2.0-beta.556", "contentHash": "llRPgmA1fhC0I0QyFLEcjvtM2239QzKr/tcnbsjArLMJxJlu0AA5G7Fft0OI30pHF3MW63Gf4aSSsjc5m82J1Q==", "dependencies": {"StyleCop.Analyzers.Unstable": "1.2.0.556"}}, "System.Linq.Async": {"type": "Direct", "requested": "[6.0.1, )", "resolved": "6.0.1", "contentHash": "0YhHcaroWpQ9UCot3Pizah7ryAzQhNvobLMSxeDIGmnXfkQn8u5owvpOH0K6EVB+z9L7u6Cc4W17Br/+jyttEQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "6.0.0"}}, "xunit.analyzers": {"type": "Direct", "requested": "[1.15.0, )", "resolved": "1.15.0", "contentHash": "s+M8K/Rtlgr6CmD7AYQKrNTvT5sh0l0ZKDoZ3Z/ExhlIwfV9mGAMR4f7KqIB7SSK7ZOhqDTgTUMYPmKfmvWUWQ=="}, "Xunit.Combinatorial": {"type": "Direct", "requested": "[1.6.24, )", "resolved": "1.6.24", "contentHash": "iqoQxBOE78VFwD9hYcxiAsJML4lAsbPNQ4lFLsyXNmr5/0W2RTM8sojUFU7rGrbUWY6VaA1E6NROI4e4lLmfIw==", "dependencies": {"xunit.extensibility.core": "2.2.0"}}, "xunit.core": {"type": "Direct", "requested": "[2.9.0, )", "resolved": "2.9.0", "contentHash": "uRaop9tZsZMCaUS4AfbSPGYHtvywWnm8XXFNUqII7ShWyDBgdchY6gyDNgO4AK1Lv/1NNW61Zq63CsDV6oH6Jg==", "dependencies": {"xunit.extensibility.core": "[2.9.0]", "xunit.extensibility.execution": "[2.9.0]"}}, "Xunit.DependencyInjection": {"type": "Direct", "requested": "[9.3.0, )", "resolved": "9.3.0", "contentHash": "1WAOh3Njy1OtZR12UtQ/oj9gtmuWaTOOKO5I3Y5+HlSOLjZlXoSHAndLGehQosu5oTs7vFjQfEM/U5dFjhtKEg==", "dependencies": {"Microsoft.Extensions.Hosting": "8.0.0", "xunit.extensibility.execution": "[2.4.2, 3.0.0)"}}, "xunit.runner.visualstudio": {"type": "Direct", "requested": "[2.8.2, )", "resolved": "2.8.2", "contentHash": "vm1tbfXhFmjFMUmS4M0J0ASXz3/U5XvXBa6DOQUL3fEz4Vt6YPhv+ESCarx6M6D+9kJkJYZKCNvJMas1+nVfmQ=="}, "xunit.v3.assert": {"type": "Direct", "requested": "[0.2.0-pre.69, )", "resolved": "0.2.0-pre.69", "contentHash": "LkcZlbvm99BbOqzuZgNYytrPhfYp+Xeo6aiK/MEct1Bpi0aZ6vDwBeoQGyNHSz5RiOsy7EZG6CE9ADeYDcXBvw=="}, "Google.OrTools.runtime.linux-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "3/he2Q/VkhLDH82/Sw6ieQUb1zMUbs6ZdhhDI2DE6mXqjeUa3lX8caSIyC+IgyXwjAN3K5MaOOLJ8QX22W4yGQ=="}, "Google.OrTools.runtime.linux-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "rldhqtf/OOBifPEoASjAv6qdOlBGSj7HXTgvyKohsA8q0ySCphZOJpYfWoO6Art0r+3FcrldWRoZs0E4iuNQZg=="}, "Google.OrTools.runtime.osx-arm64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "Kp370X4Cmen8Zno6btH45x+qUIG0vT3OaxEgMiezIjCL4nesSO4XTRhKUQwKP5DYmWzSMdfS/ZJUa5rM8L+iAw=="}, "Google.OrTools.runtime.osx-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "VFZkhe6NjrkOkVjKrQXdBgwWCKK3HrqtgSqTIgNrpM30Gb36cQuszlPZxoc6JsbTHSFJFvImUr2qlg2S4CTrXg=="}, "Google.OrTools.runtime.win-x64": {"type": "Transitive", "resolved": "9.14.6206", "contentHash": "/2XtRueL9f14BOnTjvzcIGmFXZfIYw/a5MOgJ/UDwBW9iNBgfnoHyzcTbZ0U7W5Oiex+BJtOdwekkmmvSAX1Hg=="}, "libisl.linux-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "MW+0UxLeAsVyJ6bn0yka2R5HQdCbtCg/zRZswFaWd8Qi9aVNtJmHVBIMJR1kkNk9vjYLTmGIPPUqwJTKXY7Oog=="}, "libisl.osx-arm64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "/LNMumw054jnLUMqsnOG+00ya3OpHcBlXNXtspbpQNvRnfhCMAdMc5hDanjhhU8Plq+GuLwHWS4C7rYdm/yOsA=="}, "libisl.osx-x64": {"type": "Transitive", "resolved": "0.0.4", "contentHash": "hRMKX5KF8zePmfIbH+btnPRi57bTgEhhzG6kA9koIV4fLR8XpKVKRzHh9pQotFX6LSjr297e3vm/yyStLJR1EQ=="}, "libortki": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "svfuG5mxGY/QC/5DVheHOCELmdSP90RtxQ73j23KarPXZ9ZXW+7v1l5J77hGDyQbEh1BGrnGgKBlyn76RauGHg==", "dependencies": {"libortki-linux": "0.0.2", "libortki-osx": "0.0.2", "libortki-osx-arm64": "0.0.2", "libortki-win": "0.0.2"}}, "libortki-linux": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "b04LWD4lgGy60tys3hPFhnUpgWDM6dN5r1PI7GOcPj8VupXCaI70LKNQ5/5twbDE6rkowOGanVTw0S2wBGBqBQ=="}, "libortki-osx": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "O6Q9GLULkDkZEPAZJVKLPH0ROXGVOE7BxuddgOcHNK2oiTEM7wIRnzp2OIlYgLpaOLyxJMisbGOhtWgdzt2Wng=="}, "libortki-osx-arm64": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "4Qn2dirJmRicnUG945oWpq7HVGwgqCKKxYPMISv/MRvmpZBbXrZ1cVvRaF8WwTu4XXgfKTa1sLv+i8zLifUMeQ=="}, "libortki-win": {"type": "Transitive", "resolved": "0.0.2", "contentHash": "HAoROgAKn8XBun11X43HZuspKlo5JGy8/OYw5IUPo7FVh5TCaPrLjGmyGYYZ2dqLlv31yv/b6s254PIRGn95cA=="}, "Microsoft.Bcl.AsyncInterfaces": {"type": "Transitive", "resolved": "7.0.0", "contentHash": "3aeMZ1N0lJoSyzqiP03hqemtb1BijhsJADdobn/4nsMJ8V1H+CrpuduUe4hlRdx+ikBQju1VGjMD1GJ3Sk05Eg=="}, "Microsoft.CodeCoverage": {"type": "Transitive", "resolved": "17.10.0", "contentHash": "yC7oSlnR54XO5kOuHlVOKtxomNNN1BWXX8lK1G2jaPXT9sUok7kCOoA4Pgs0qyFaCtMrNsprztYMeoEGqCm4uA=="}, "Microsoft.CSharp": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "pTj+D3uJWyN3My70i2Hqo+OXixq3Os2D1nJ2x92FFo6sk8fYS1m1WLNTs0Dc1uPaViH0YvEEwvzddQ7y4rhXmA=="}, "Microsoft.Extensions.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0J/9YNXTMWSZP2p2+nvl8p71zpSwokZXZuJW+VjdErkegAnFdO1XlqtA62SJtgVYHdKu3uPxJHcMR/r35HwFBA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3lE/iLSutpgX1CC0NOW70FJoGARRHbyKmG7dc0klnUZ9Dd9hS6N/POPWhKhMLCEuNN5nXEY5agmlFtH562vqhQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Binder": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "mBMoXLsr5s1y2zOHWmKsE9veDcx8h1x/c3rz4baEdQKTeDcmQAPNbB54Pi/lhFO3K431eEq6PFbMgLaa6PHFfA==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.CommandLine": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "NZuZMz3Q8Z780nKX3ifV1fE7lS+6pynDHK71OfU4OZ1ItgvDOhyOC7E6z+JMZrAj63zRpwbdldYFk499t3+1dQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.EnvironmentVariables": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "plvZ0ZIpq+97gdPNNvhwvrEZ92kNml9hd1pe3idMA7svR0PztdzVLkoWLcRFgySYXUJc3kSM3Xw3mNFMo/bxRA==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Configuration.FileExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "McP+Lz/EKwvtCv48z0YImw+L1gi1gy5rHhNaNIY2CrjloV+XY8gydT8DjMR6zWeL13AFK+DioVpppwAuO1Gi1w==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Configuration.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "C2wqUoh9OmRL1akaCcKSTmRU8z0kckfImG7zLNI8uyi47Lp+zd5LWAD17waPQEqCz3ioWOCrFUo+JJuoeZLOBw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.FileExtensions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Configuration.UserSecrets": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ihDHu2dJYQird9pl2CbdwuNDfvCZdOS0S7SPlNfhPt0B81UTT+yyZKz2pimFZGUp3AfuBRnqUCxB2SjsZKHVUw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Json": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Physical": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "V8S3bsm50ig6JSyrbcJJ8bW2b9QLGouz+G1miK3UTaOWmMtFwNNNzUf4AleyDWUmTrWMLNnFSLEQtxmxgNQnNQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0"}}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"type": "Transitive", "resolved": "8.0.1", "contentHash": "fGLiCRLMYd00JYpClraLjJTNKLmMJPnqxMaiRzEBIIvevlzxz33mXy39Lkd48hu1G+N21S7QpaO5ZzKsI6FRuA=="}, "Microsoft.Extensions.Diagnostics": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3PZp/YSkIXrF7QK7PfC1bkyRYwqOHpWFad8Qx+4wkuumAeXo1NHaxpS9LboNA9OvNSAu+QOVlXbMyoY+pHSqcw==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Diagnostics.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "JHYCQG7HmugNYUhOl368g+NMxYE/N/AiclCYRNlgCY9eVyiBkOHMwK4x60RYMxv9EL3+rmj1mqHvdCiPpC+D4Q==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.DiagnosticSource": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Abstractions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ZbaMlhJlpisjuWbvXr4LdAst/1XxH3vZ6A0BsgTphZ2L4PGuxRLz7Jr/S7mkAAnOn78Vu0fKhEgNF5JO3zfjqQ==", "dependencies": {"Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileProviders.Physical": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "UboiXxpPUpwulHvIAVE36Knq0VSHaAmfrFkegLyBZeaADuKezJ/AIXYAW8F5GBlGk/VaibN2k/Zn1ca8YAfVdA==", "dependencies": {"Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.FileSystemGlobbing": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.FileSystemGlobbing": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OK+670i7esqlQrPjdIKRbsyMCe9g5kSLpRRQGSr4Q58AOYEe/hCnfLZprh7viNisSUUQZmMrbbuDaIrP+V1ebQ=="}, "Microsoft.Extensions.Logging": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "tvRkov9tAJ3xP51LCv3FJ2zINmv1P8Hi8lhhtcKGqM+ImiTCC84uOPEI4z8Cdq2C3o9e+Aa0Gw0rmrsJD77W+w==", "dependencies": {"Microsoft.Extensions.DependencyInjection": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0"}}, "Microsoft.Extensions.Logging.Configuration": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "ixXXV0G/12g6MXK65TLngYN9V5hQQRuV+fZi882WIoVJT7h5JvoYoxTEwCgdqwLjSneqh1O+66gM8sMr9z/rsQ==", "dependencies": {"Microsoft.Extensions.Configuration": "8.0.0", "Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Options.ConfigurationExtensions": "8.0.0"}}, "Microsoft.Extensions.Logging.Console": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "e+48o7DztoYog+PY430lPxrM4mm3PbA6qucvQtUDDwVo4MO+ejMw7YGc/o2rnxbxj4isPxdfKFzTxvXMwAz83A==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Configuration": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Logging.Debug": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "dt0x21qBdudHLW/bjMJpkixv858RRr8eSomgVbU8qljOyfrfDGi1JQvpF9w8S7ziRPtRKisuWaOwFxJM82GxeA==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "3X9D3sl7EmOu7vQp5MJrmIJBl5XSdOhZPYXUeFfYa6Nnm9+tok8x3t3IVPLhm7UJtPOU61ohFchw8rNm9tIYOQ==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "System.Diagnostics.EventLog": "8.0.0"}}, "Microsoft.Extensions.Logging.EventSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "oKcPMrw+luz2DUAKhwFXrmFikZWnyc8l2RKoQwqU3KIZZjcfoJE0zRHAnqATfhRZhtcbjl/QkiY2Xjxp0xu+6w==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Logging": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0", "System.Text.Json": "8.0.0"}}, "Microsoft.Extensions.Options.ConfigurationExtensions": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "0f4DMRqEd50zQh+UyJc+/HiBsZ3vhAQALgdkcQEalSH1L2isdC7Yj54M3cyo5e+BeO5fcBQ7Dxly8XiBBcvRgw==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.Configuration.Binder": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Options": "8.0.0", "Microsoft.Extensions.Primitives": "8.0.0"}}, "Microsoft.Extensions.Primitives": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "bXJEZrW9ny8vjMF1JV253WeLhpEVzFo1lyaZu1vQ4ZxWUlVvknZ/+ftFgVheLubb4eZPSwwxBeqS1JkCOjxd8g=="}, "Microsoft.NETCore.Platforms": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "kz0PEW2lhqygehI/d6XsPCQzD7ff7gUJaVGPVETX611eadGsA3A877GdSlU0LRVMCTH/+P3o2iDTak+S08V2+A=="}, "Microsoft.NETCore.Targets": {"type": "Transitive", "resolved": "1.1.0", "contentHash": "aOZA3BWfz9RXjpzt0sRJJMjAscAUm3Hoa4UWAfceV9UTYxgwZ1lZt5nO2myFf+/jetYQo4uTP7zS8sJY67BBxg=="}, "Microsoft.TestPlatform.TestHost": {"type": "Transitive", "resolved": "17.10.0", "contentHash": "LWpMdfqhHvcUkeMCvNYJO8QlPLlYz9XPPb+ZbaXIKhdmjAV0wqTSrTiW5FLaf7RRZT50AQADDOYMOe0HxDxNgA==", "dependencies": {"Microsoft.TestPlatform.ObjectModel": "17.10.0", "Newtonsoft.Json": "13.0.1"}}, "NetFabric.Hyperlinq.Abstractions": {"type": "Transitive", "resolved": "1.3.0", "contentHash": "WXnEcGwmXfa8gW9N2MlcaPNUzM3NLMwnAhacbtH554F8YcoXbIkTB+uGa1Aa+9gyb/9JZgYVHnmADgJUKP52nA=="}, "NETStandard.Library": {"type": "Transitive", "resolved": "2.0.3", "contentHash": "st47PosZSHrjECdjeIzZQbzivYBJFv6P2nv4cj2ypdI204DO+vZ7l5raGMiX4eXMJ53RfOIg+/s4DHVZ54Nu2A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0"}}, "StyleCop.Analyzers.Unstable": {"type": "Transitive", "resolved": "1.2.0.556", "contentHash": "zvn9Mqs/ox/83cpYPignI8hJEM2A93s2HkHs8HYMOAQW0PkampyoErAiIyKxgTLqbbad29HX/shv/6LGSjPJNQ=="}, "System.Buffers": {"type": "Transitive", "resolved": "4.5.1", "contentHash": "Rw7ijyl1qqRS0YQD/WycNst8hUUMgrMH4FCn1nNm27M4VxchZ1js3fVjQaANHO5f3sN4isvP4a+Met9Y4YomAg=="}, "System.Collections": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3Dcj85/TBdVpL5Zr+gEEBUuFe2icOnLalmEh9hfck1PTYbbyWuZgh4fmm2ysCLTrqLQw6t3TgTyJ+VLp+Qb+Lw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.Contracts": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "eelRRbnm+OloiQvp9CXS0ixjNQldjjkHO4iIkR5XH2VIP8sUB/SIpa1TdUW6/+HDcQ+MlhP3pNa1u5SbzYuWGA==", "dependencies": {"System.Runtime": "4.3.0"}}, "System.Diagnostics.Debug": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "ZUhUOdqmaG5Jk3Xdb8xi5kIyQYAA4PnTNlHx1mu9ZY3qv4ELIdKbnL/akbGaKi2RnNUWaZsAs31rvzFdewTj2g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Diagnostics.DiagnosticSource": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "c9xLpVz6PL9lp/djOWtk5KPDZq3cSYpmXoJQY524EOtuFl5z9ZtsotpsyrDW40U1DRnQSYvcPKEUV0X//u6gkQ=="}, "System.Diagnostics.EventLog": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "fdYxcRjQqTTacKId/2IECojlDSFvp7LP5N78+0z/xH7v/Tuw5ZAxu23Y6PTCRinqyu2ePx+Gn1098NC6jM6d+A=="}, "System.Globalization": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "kYdVd2f2PAdFGblzFswE4hkNANJBKRmsfa2X5LG2AcWE1c7/4t0pYae1L8vfZ5xvE2nK/R9JprtToA61OSHWIg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.IO": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "3qjaHvxQPDpSOYICjUoTsmoq5u6QJAFRUITgeT/4gqkF1bajbSmb1kwSxEA8AHlofqgcKJcM8udgieRNhaJ5Cg==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0", "System.Text.Encoding": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Linq": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5DbqIUpsDp0dFftytzuMmc0oeMdQwjcP/EWxsksIz/w1TcFRkZ3yKKz0PqiYFMmEwPSWw+qNVqD7PJ889JzHbw==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0"}}, "System.Linq.Expressions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "PGKkrd2khG4CnlyJwxwwaWWiSiWFNBGlgXvJpeO0xCXrZ89ODrQ6tjEWS/kOqZ8GwEOUATtKtzp1eRgmYNfclg==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Globalization": "4.3.0", "System.IO": "4.3.0", "System.Linq": "4.3.0", "System.ObjectModel": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Emit": "4.3.0", "System.Reflection.Emit.ILGeneration": "4.3.0", "System.Reflection.Emit.Lightweight": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Reflection.TypeExtensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Runtime.Extensions": "4.3.0", "System.Threading": "4.3.0"}}, "System.Linq.Queryable": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "In1Bmmvl/j52yPu3xgakQSI0YIckPUr870w4K5+Lak3JCCa8hl+my65lABOuKfYs4ugmZy25ScFerC4nz8+b6g==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Expressions": "4.3.0", "System.Reflection": "4.3.0", "System.Reflection.Extensions": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Memory": {"type": "Transitive", "resolved": "4.5.5", "contentHash": "XIWiDvKPXaTveaB7HVganDlOCRoj03l+jrwNvcge/t8vhGYKvqV+dMv6G4SAX2NoNmN0wZfVPTAlFwZcZvVOUw=="}, "System.ObjectModel": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "bdX+80eKv9bN6K4N+d77OankKHGn6CH711a6fcOpMQu2Fckp/Ft4L/kW9WznHpyR0NRAvJutzOMHNNlBGvxQzQ==", "dependencies": {"System.Collections": "4.3.0", "System.Diagnostics.Debug": "4.3.0", "System.Resources.ResourceManager": "4.3.0", "System.Runtime": "4.3.0", "System.Threading": "4.3.0"}}, "System.Reflection": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "KMiAFoW7MfJGa9nDFNcfu+FpEdiHpWgTcS2HdMpDvt9saK3y/G4GwprPyzqjFH9NTaGPQeWNHU+iDlDILj96aQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.IO": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "VR4kk8XLKebQ4MZuKuIni/7oh+QGFmZW3qORd1GvBq/8026OpW501SzT/oypwiQl4TvT8ErnReh/NzY9u+C6wQ=="}, "System.Reflection.Emit.ILGeneration": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "59tBslAk9733NXLrUJrwNZEzbMAcu8k344OYo+wfSVygcgZ9lgBdGIzH/nrg3LYhXceynyvTc8t5/GD4Ri0/ng==", "dependencies": {"System.Reflection": "4.3.0", "System.Reflection.Primitives": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Emit.Lightweight": {"type": "Transitive", "resolved": "4.7.0", "contentHash": "a4OLB4IITxAXJeV74MDx49Oq2+PsF6Sml54XAFv+2RyWwtDBcabzoxiiJRhdhx+gaohLh4hEGCLQyBozXoQPqA=="}, "System.Reflection.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "rJkrJD3kBI5B712aRu4DpSIiHRtr6QlfZSQsb0hYHrDCZORXCFjQfoipo2LaMUHoT9i1B7j7MnfaEKWDFmFQNQ==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Reflection.Metadata": {"type": "Transitive", "resolved": "1.6.0", "contentHash": "COC1aiAJjCoA5GBF+QKL2uLqEBew4JsCkQmoHKbN3TlOZKa2fKLz5CpiRQKDz0RsAOEGsVKqOD5bomsXq/4STQ=="}, "System.Reflection.Primitives": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "5RXItQz5As4xN2/YUDxdpsEkMhvw3e6aNveFXUn4Hl/udNTCNhnKp8lT9fnc3MhvGKh1baak5CovpuQUXHAlIA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Reflection.TypeExtensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "7u6ulLcZbyxB5Gq0nMkQttcdBTx57ibzw+4IOXEfR+sXYQoHvjW5LTLyNr8O22UIMrqYbchJQJnos4eooYzYJA==", "dependencies": {"System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Resources.ResourceManager": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "/zrcPkkWdZmI4F92gL/TPumP98AVDu/Wxr3CSJGQQ+XN6wbRZcyfSKVoPo17ilb3iOr0cCRqJInGwNMolqhS8A==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Globalization": "4.3.0", "System.Reflection": "4.3.0", "System.Runtime": "4.3.0"}}, "System.Runtime": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "JufQi0vPQ0xGnAczR13AUFglDyVYt4Kqnz1AZaiKZ5+GICq0/1MH/mO/eAJHt/mHW1zjKBJd7kV26SrxddAhiw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0"}}, "System.Runtime.CompilerServices.Unsafe": {"type": "Transitive", "resolved": "5.0.0", "contentHash": "ZD9TMpsmYJLrxbbmdvhwt9YEgG5WntEnZ/d1eH8JBX9LBp+Ju8BSBhUGbZMNVHHomWo2KVImJhTDl2hIgw/6MA=="}, "System.Runtime.Extensions": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "guW0uK0fn5fcJJ1tJVXYd7/1h5F+pea1r7FLSOz/f8vPEqbR2ZAknuRDvTQ8PzAilDveOxNjSfr0CHfIQfFk8g==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encoding": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "BiIg+KWaSDOITze6jGQynxg64naAPtqGHBwDrLaCtixsa5bKiR8dpPOHA7ge3C0JJQizJE+sfkz1wV+BAKAYZw==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Text.Encodings.Web": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "yev/k9GHAEGx2Rg3/tU6MQh4HGBXJs70y7j1LaM1i/ER9po+6nnQ6RRqTJn1E7Xu0fbIFK80Nh5EoODxrbxwBQ=="}, "System.Text.Json": {"type": "Transitive", "resolved": "8.0.0", "contentHash": "OdrZO2WjkiEG6ajEFRABTRCi/wuXQPxeV6g8xvUJqdxMvvuCCEk86zPla8UiIQJz3durtUEbNyY/3lIhS0yZvQ==", "dependencies": {"System.Text.Encodings.Web": "8.0.0"}}, "System.Threading": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "VkUS0kOBcUf3Wwm0TSbrevDDZ6BlM+b/HRiapRFWjM5O0NS0LviG0glKmFK+hhPDd1XFeSdU1GmlLhb2CoVpIw==", "dependencies": {"System.Runtime": "4.3.0", "System.Threading.Tasks": "4.3.0"}}, "System.Threading.Tasks": {"type": "Transitive", "resolved": "4.3.0", "contentHash": "LbSxKEdOUhVe8BezB/9uOGGppt+nZf6e1VFyw6v3DN6lqitm0OSn2uXMOdtP0M3W4iMcqcivm2J6UgqiwwnXiA==", "dependencies": {"Microsoft.NETCore.Platforms": "1.1.0", "Microsoft.NETCore.Targets": "1.1.0", "System.Runtime": "4.3.0"}}, "System.Threading.Tasks.Extensions": {"type": "Transitive", "resolved": "4.5.4", "contentHash": "zteT+G8xuGu6mS+mzDzYXbzS7rd3K6Fjb9RiZlYlJPam2/hU7JCBZBVEcywNuR+oZ1ncTvc/cq0faRr3P01OVg=="}, "System.ValueTuple": {"type": "Transitive", "resolved": "4.5.0", "contentHash": "okurQJO6NRE/apDIP23ajJ0hpiNmJ+f0BwOlB/cSqTLQlw5upkf+5+96+iG2Jw40G1fCVCyPz/FhIABUjMR+RQ=="}, "xunit.abstractions": {"type": "Transitive", "resolved": "2.0.3", "contentHash": "pot1I4YOxlWjIb5jmwvvQNbTrZ3lJQ+jUGkGjWE3hEFM0l5gOnBWS+H3qsex68s5cO52g+44vpGzhAt+42vwKg=="}, "xunit.extensibility.core": {"type": "Transitive", "resolved": "2.9.0", "contentHash": "zjDEUSxsr6UNij4gIwCgMqQox+oLDPRZ+mubwWLci+SssPBFQD1xeRR4SvgBuXqbE0QXCJ/STVTp+lxiB5NLVA==", "dependencies": {"xunit.abstractions": "2.0.3"}}, "xunit.extensibility.execution": {"type": "Transitive", "resolved": "2.9.0", "contentHash": "5ZTQZvmPLlBw6QzCOwM0KnMsZw6eGjbmC176QHZlcbQoMhGIeGcYzYwn5w9yXxf+4phtplMuVqTpTbFDQh2bqQ==", "dependencies": {"xunit.extensibility.core": "[2.9.0]"}}, "nncase.codegen": {"type": "Project", "dependencies": {"Extension.Mathematics": "[1.2.12, )", "Nncase.Core": "[1.0.0, )", "Nncase.IO": "[1.0.0, )"}}, "nncase.compiler": {"type": "Project", "dependencies": {"DryIoc.Microsoft.DependencyInjection": "[6.2.0, )", "DryIoc.dll": "[5.4.3, )", "Microsoft.Extensions.Hosting": "[8.0.0, )", "Nncase.CodeGen": "[1.0.0, )", "Nncase.Core": "[1.0.0, )", "Nncase.Diagnostics": "[1.0.0, )", "Nncase.EGraph": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )", "Nncase.Importer": "[1.0.0, )", "Nncase.Modules.NTT": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "Nncase.Quantization": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )", "Nncase.Simulator": "[1.0.0, )", "Razor.Templating.Core": "[2.0.0, )"}}, "nncase.core": {"type": "Project", "dependencies": {"CommunityToolkit.HighPerformance": "[8.2.2, )", "DryIoc.dll": "[5.4.3, )", "GiGraph.Dot": "[3.0.1, )", "Google.OrTools": "[9.14.6206, )", "ISLSharp": "[1.0.4, )", "Microsoft.Extensions.Hosting.Abstractions": "[8.0.0, )", "Microsoft.Extensions.Logging.Abstractions": "[8.0.1, )", "Microsoft.Extensions.Options": "[8.0.2, )", "NetFabric.Hyperlinq": "[3.0.0-beta48, )", "System.CommandLine": "[2.0.0-beta4.22272.1, )", "System.Reactive": "[6.0.0, )"}}, "nncase.diagnostics": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )"}}, "nncase.egraph": {"type": "Project", "dependencies": {"GiGraph.Dot": "[3.0.1, )", "Google.OrTools": "[9.14.6206, )", "NetFabric.Hyperlinq": "[3.0.0-beta48, )", "Nncase.Core": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )"}}, "nncase.evaluator": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "OrtKISharp": "[0.0.2, )"}}, "nncase.graph": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "QuikGraph": "[2.5.0, )", "QuikGraph.Graphviz": "[2.5.0, )"}}, "nncase.importer": {"type": "Project", "dependencies": {"Clawfoot.Extensions.Newtonsoft": "[0.1.0, )", "LanguageExt.Core": "[4.4.9, )", "Nncase.Core": "[1.0.0, )", "Onnx.Protobuf": "[1.0.0, )", "TFLite.Schema": "[1.0.0, )"}}, "nncase.io": {"type": "Project"}, "nncase.modules.ntt": {"type": "Project", "dependencies": {"Nncase.CodeGen": "[1.0.0, )", "Nncase.Diagnostics": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )", "Nncase.Targets": "[1.0.0, )", "Razor.Templating.Core": "[2.0.0, )"}}, "nncase.passes": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.EGraph": "[1.0.0, )", "Nncase.Evaluator": "[1.0.0, )", "Nncase.Graph": "[1.0.0, )", "QuikGraph.Graphviz": "[2.5.0, )"}}, "nncase.quantization": {"type": "Project", "dependencies": {"Newtonsoft.Json": "[13.0.3, )", "Nncase.Core": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "OrtKISharp": "[0.0.2, )", "System.Linq.Async": "[6.0.1, )"}}, "nncase.schedule": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )"}}, "nncase.simulator": {"type": "Project", "dependencies": {"Nncase.Core": "[1.0.0, )"}}, "nncase.targets": {"type": "Project", "dependencies": {"Nncase.CodeGen": "[1.0.0, )", "Nncase.Core": "[1.0.0, )", "Nncase.Schedule": "[1.0.0, )"}}, "nncase.tests.testfixture": {"type": "Project", "dependencies": {"MethodBoundaryAspect.Fody": "[2.0.149, )", "NETStandard.Library": "[2.0.3, )", "Nncase.CodeGen": "[1.0.0, )", "Nncase.Compiler": "[1.0.0, )", "Nncase.Core": "[1.0.0, )", "Nncase.Modules.NTT": "[1.0.0, )", "Nncase.Passes": "[1.0.0, )", "Nncase.Simulator": "[1.0.0, )", "System.Linq.Async": "[6.0.1, )", "Xunit.Combinatorial": "[1.6.24, )", "Xunit.DependencyInjection": "[9.3.0, )", "xunit.v3.assert": "[0.2.0-pre.69, )"}}, "onnx.protobuf": {"type": "Project", "dependencies": {"Google.Protobuf": "[3.27.3, )"}}, "tflite.schema": {"type": "Project", "dependencies": {"Nncase.FlatBuffers": "[2.0.0, )"}}, "Clawfoot.Extensions.Newtonsoft": {"type": "CentralTransitive", "requested": "[0.1.0, )", "resolved": "0.1.0", "contentHash": "A8p8THcOiOoexdYUUHgEVeW2BgsFLRqm65+4WuE3Te0XyZdiq+3Alu0D8ktMFUU+0eFeXr6sYcNsDTD3OsVJ4w==", "dependencies": {"Newtonsoft.Json": "12.0.2"}}, "DryIoc.dll": {"type": "CentralTransitive", "requested": "[5.4.3, )", "resolved": "5.4.3", "contentHash": "yhXOG3SOxeWYxBAWskNRDD8fzw5hriEawv4VN4WKaFHOuubiop4kxe2rkWTEyCnTgRVgxVUSQCDBBkZqwPm1iQ=="}, "DryIoc.Microsoft.DependencyInjection": {"type": "CentralTransitive", "requested": "[6.2.0, )", "resolved": "6.2.0", "contentHash": "vR0CUZ/H/1WamB9VDHyBg8nTxqGAU8/hJo4MdDQrwfdsm1E2d9HrJNDJo+V8gAg0GXpC1vgHv7JenpehT0iybw==", "dependencies": {"DryIoc.dll": "5.4.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "7.0.0"}}, "Extension.Mathematics": {"type": "CentralTransitive", "requested": "[1.2.12, )", "resolved": "1.2.12", "contentHash": "D4mn5Cab4ztPLJ0V8uMErDrO/Y61098nwrvyIOLZymVAYOQcwP1vomVWKbTagf1aPU3cX5Q7adZtQEQwOy6XEg=="}, "GiGraph.Dot": {"type": "CentralTransitive", "requested": "[3.0.1, )", "resolved": "3.0.1", "contentHash": "TseDWNkBFKrrrzamgl2yeSDk9gmxQZvymcTqRydY/EL9REM9jBwCHFceg7AutXw3ULUL/FKhc1uFzfd2Cjpe/A=="}, "Google.OrTools": {"type": "CentralTransitive", "requested": "[9.14.6206, )", "resolved": "9.14.6206", "contentHash": "HLZCX691VXzR5x6YAHUxHvxHC+KyXJIiqKiS/Fu/g23W8XZgmfSGctnchVkrUT8a9Sosyvp1G0GQlV/i9PdVNQ==", "dependencies": {"Google.OrTools.runtime.linux-arm64": "9.14.6206", "Google.OrTools.runtime.linux-x64": "9.14.6206", "Google.OrTools.runtime.osx-arm64": "9.14.6206", "Google.OrTools.runtime.osx-x64": "9.14.6206", "Google.OrTools.runtime.win-x64": "9.14.6206", "Google.Protobuf": "3.31.1"}}, "Google.Protobuf": {"type": "CentralTransitive", "requested": "[3.27.3, )", "resolved": "3.31.1", "contentHash": "gSnJbUmGiOTdWddPhqzrEscHq9Ls6sqRDPB9WptckyjTUyx70JOOAaDLkFff8gManZNN3hllQ4aQInnQyq/Z/A=="}, "ISLSharp": {"type": "CentralTransitive", "requested": "[1.0.4, )", "resolved": "1.0.4", "contentHash": "TZabyHb9+wLsBAc7OUxzHJw3BeJMGXMG4WAVoBpbFJTsBRd64rFepFY80K7iGRungi4GHipt6cj/2vgsHuCwlw==", "dependencies": {"libisl.linux-x64": "0.0.4", "libisl.osx-arm64": "0.0.4", "libisl.osx-x64": "0.0.4"}}, "LanguageExt.Core": {"type": "CentralTransitive", "requested": "[4.4.9, )", "resolved": "4.4.9", "contentHash": "K9VGWkThJkaomifa3zcmwysw1BaSqIZZPZc6trBnJN8u9mpmA/cMMwCWEa/v7bPv/+NnG6PbyIDB7HtxBX7yCQ==", "dependencies": {"Microsoft.Bcl.AsyncInterfaces": "7.0.0", "Microsoft.CSharp": "4.7.0", "System.Diagnostics.Contracts": "4.3.0", "System.Linq": "4.3.0", "System.Linq.Queryable": "4.3.0", "System.Memory": "4.5.5", "System.Reflection.Emit": "4.7.0", "System.Reflection.Emit.Lightweight": "4.7.0", "System.Threading.Tasks.Extensions": "4.5.4", "System.ValueTuple": "4.5.0"}}, "Microsoft.Extensions.Hosting.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.0, )", "resolved": "8.0.0", "contentHash": "AG7HWwVRdCHlaA++1oKDxLsXIBxmDpMPb3VoyOoAghEWnkUvEAdYQUwnV4jJbAaa/nMYNiEh5ByoLauZBEiovg==", "dependencies": {"Microsoft.Extensions.Configuration.Abstractions": "8.0.0", "Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.0", "Microsoft.Extensions.Diagnostics.Abstractions": "8.0.0", "Microsoft.Extensions.FileProviders.Abstractions": "8.0.0", "Microsoft.Extensions.Logging.Abstractions": "8.0.0"}}, "Microsoft.Extensions.Logging.Abstractions": {"type": "CentralTransitive", "requested": "[8.0.1, )", "resolved": "8.0.1", "contentHash": "RIFgaqoaINxkM2KTOw72dmilDmTrYA0ns2KW4lDz4gZ2+o6IQ894CzmdL3StM2oh7QQq44nCWiqKqc4qUI9Jmg==", "dependencies": {"Microsoft.Extensions.DependencyInjection.Abstractions": "8.0.1"}}, "NetFabric.Hyperlinq": {"type": "CentralTransitive", "requested": "[3.0.0-beta48, )", "resolved": "3.0.0-beta48", "contentHash": "oYUhXvxNS8bBJWqNkvx5g8y0P/0LtyqS2pN0w4OWjVDNWEpLbdbvPy9w/9z1n2PrqIjX3jxUsEnoCmxxGnI3gw==", "dependencies": {"NetFabric.Hyperlinq.Abstractions": "1.3.0", "System.Buffers": "4.5.1", "System.Runtime.CompilerServices.Unsafe": "5.0.0"}}, "Newtonsoft.Json": {"type": "CentralTransitive", "requested": "[13.0.3, )", "resolved": "13.0.3", "contentHash": "HrC5BXdl00IP9zeV+0Z848QWPAoCr9P3bDEZguI+gkLcBKAOxix/tLEAAHC+UvDNPv4a2d18lOReHMOagPa+zQ=="}, "Nncase.FlatBuffers": {"type": "CentralTransitive", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "ir3uek0+7Y8SwOUGUR8y94sgpVDWLAjKGBm9z7cLe/38GyPxWbIYHPnHZHksNTExTsx3Ie9GtwagkgR/jm64hA=="}, "OrtKISharp": {"type": "CentralTransitive", "requested": "[0.0.2, )", "resolved": "0.0.2", "contentHash": "q8j0yR5836Zhv9WB9BFkQt1UaEFyibq8bqJcTiULlILF6/sz8z7Wy2N8sgYdDKsdW25zncIz7j6IDbKM5ynePg==", "dependencies": {"libortki": "0.0.2"}}, "QuikGraph": {"type": "CentralTransitive", "requested": "[2.5.0, )", "resolved": "2.5.0", "contentHash": "sG+mrPpXwxlXknRK5VqWUGiOmDACa9X+3ftlkQIMgOZUqxVOQSe0+HIU9PTjwqazy0pqSf8MPDXYFGl0GYWcKw=="}, "QuikGraph.Graphviz": {"type": "CentralTransitive", "requested": "[2.5.0, )", "resolved": "2.5.0", "contentHash": "pCKpErtHGxUi72OT+2aIg1pdHdUqpqEM5J/i9rmVsEVDE4X0xb1HBPWdxv/FLZmbBjk0ZogZXZttUL3CnAPpNw==", "dependencies": {"QuikGraph": "2.5.0"}}, "Razor.Templating.Core": {"type": "CentralTransitive", "requested": "[2.0.0, )", "resolved": "2.0.0", "contentHash": "KUsCFq6RYUbFk9NuFLgYNiw+mzmNWkSGv6jzbJAzsgICgaS6e/T+BY+IFeuUIhlDNJE+eZjF4RXCFKizM7bGPA=="}, "System.CommandLine": {"type": "CentralTransitive", "requested": "[2.0.0-beta4.22272.1, )", "resolved": "2.0.0-beta4.22272.1", "contentHash": "1uqED/q2H0kKoLJ4+hI2iPSBSEdTuhfCYADeJrAqERmiGQ2NNacYKRNEQ+gFbU4glgVyK8rxI+ZOe1onEtr/Pg=="}, "System.Reactive": {"type": "CentralTransitive", "requested": "[6.0.0, )", "resolved": "6.0.0", "contentHash": "31kfaW4ZupZzPsI5PVe77VhnvFF55qgma7KZr/E0iFTs6fmdhhG8j0mgEx620iLTey1EynOkEfnyTjtNEpJzGw=="}}}}