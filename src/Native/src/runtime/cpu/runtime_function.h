/* Copyright 2019-2021 Canaan Inc.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once
#include "runtime_module.h"
#include <nncase/ntt/arch/cpu/runtime.h>
#include <nncase/runtime/host_buffer.h>
#include <nncase/runtime/runtime_function.h>
#include <nncase/tensor.h>
#include <vector>

BEGIN_NS_NNCASE_RT_MODULE(cpu)

class cpu_runtime_function final : public runtime_function {
  public:
    cpu_runtime_function(runtime_module &rt_module);
    virtual ~cpu_runtime_function();

    cpu_runtime_module &module() const noexcept;

    const std::span<std::byte>
    block_local_data(size_t block_id) const noexcept {
        auto &local_data = local_datas_[block_id];
        auto mapped_local_data =
            local_data->map(map_read_write).expect("Failed to map local data");
        return mapped_local_data.buffer();
    }

  protected:
    result<void>
    initialize_core(runtime_function_init_context &context) noexcept override;
    result<value_t> invoke_core(std::span<value_t> parameters,
                                value_t return_value) noexcept override;

  private:
    result<void> run(std::byte *output_data) noexcept;
    result<tensor> create_output_tensor(size_t output_id,
                                        std::span<value_t> parameters,
                                        std::byte *output_data) noexcept;

  private:
    block_entry_t block_entry_;
    std::vector<host_buffer_t> local_datas_;
    host_buffer_t output_buffer_;
    std::vector<ntt::runtime::thread_inout_desc> input_descs_;
    std::vector<ntt::runtime::thread_inout_desc> output_descs_;
    std::vector<dims_t> output_shapes_;
    std::vector<dims_t> output_strides_;
};

END_NS_NNCASE_RT_MODULE
