<#@ template debug="false" hostspecific="false" language="C#" #>
<#@ assembly name="System.Core" #>
<#@ import namespace="System.IO" #>
<#@ import namespace="System.Linq" #>
<#@ import namespace="System.Text" #>
<#@ import namespace="System.Collections.Generic" #>
<#@ output extension=".cs" #>
//---------------------------------------------------------------------------------------------------
// <auto-generated>
//    This code was generated by T4 template.
//    Changes to this file may cause incorrect behavior and will be lost if the code is regenerated.
// </auto-generated>
//---------------------------------------------------------------------------------------------------

using System.Numerics;
using System.Runtime.CompilerServices;
using System.Runtime.InteropServices;
using CommunityToolkit.HighPerformance;
using System.Diagnostics.CodeAnalysis;
using System.Globalization;

namespace Nncase;

// NOTE fixed array not suppot generic

<#
var lanesValues = new[] { 
  (0, 4), (0, 8), (0, 16), (0, 32), (0, 64), (0, 128),
  (2, 4), (2, 8), (2, 16), (2, 32),
  (4, 4), (4, 8), (4, 16), (4, 32),
  (8, 8), (16, 16), (32, 16), (32, 32), (32, 64), (32, 128), (64, 32), (64, 64), (64, 128), (128, 64) };
#>
public partial record VectorType
{
    public override Type CLRType => Lanes.ToArray() switch
    {
<#
foreach (var (lanesA, lanesB) in lanesValues)
{
    var typePrefix = lanesA == 0 ? $"Vector{lanesB.ToString()}" : $"Vector{lanesA.ToString()}x{lanesB.ToString()}";
    var typeName = typePrefix + "<>";
    var lanes = lanesA == 0 ? $"[{lanesB}]" : $"[{lanesA}, {lanesB}]";#>
        <#=lanes#> => typeof(<#=typeName#>).MakeGenericType(ElemType.CLRType),
<#}#>
        _ => throw new NotSupportedException($"Unsupported vector lanes: {string.Join(", ", Lanes)}"),
    };
}

public partial record DataType
{
    private static Dictionary<Type, int[]> _clrTypeLanes = new()
    {
<#
foreach (var (lanesA, lanesB) in lanesValues)
{
    var typePrefix = lanesA == 0 ? $"Vector{lanesB.ToString()}" : $"Vector{lanesA.ToString()}x{lanesB.ToString()}";
    var typeName = typePrefix + "<>";
    var lanes = lanesA == 0 ? $"[{lanesB}]" : $"[{lanesA}, {lanesB}]";#>
        { typeof(<#=typeName#>), <#=lanes#> },
<#}#>
    };
}

<# 
foreach (var (lanesA, lanesB) in lanesValues)
{
var typePrefix = lanesA == 0 ? $"Vector{lanesB.ToString()}" : $"Vector{lanesA.ToString()}x{lanesB.ToString()}";
var typeName = typePrefix + "<T>";
#>
[StructLayout(LayoutKind.Sequential)]
public unsafe struct <#=typeName#> : IVector<T>, IEquatable<<#=typeName#>>
    where T : unmanaged, IEquatable<T>
{
<# if (lanesA == 0) {#>
<# foreach (var i in Enumerable.Range(0, lanesB)) { #>
    private T _item_0_<#=i#>;
<#}#>
<#} else {#>
<# foreach (var i in Enumerable.Range(0, lanesA)) { 
   foreach (var j in Enumerable.Range(0, lanesB)) {#>
    private T _item_<#=i#>_<#=j#>;
<#}}#>
<#}#>

    static <#=typePrefix#>()
    {
        if (typeof(T) == typeof(bool))
        {
            throw new ArgumentException("Boolean is not supported in vector type.");
        }
    }

    public static <#=typeName#> Create(T[] array) 
    {
        <#=typeName#> vec = default;
        var src = array.AsSpan();
        var dest = vec.AsSpan();
        src.CopyTo(dest);
        return vec;
    }
<# if (lanesA != 0) {#>

    public static <#=typeName#> Create(T[,] array) 
    {
        <#=typeName#> vec = default;
        var src = array.AsSpan2D();
        var dest = vec.AsSpan2D();
        src.CopyTo(dest);
        return vec;
    }
<#}#>

<# if (lanesA == 0) {#>
    public T this[int i]
    {
        get => Unsafe.Add(ref Unsafe.AsRef(in _item_0_0), i);
        set => Unsafe.Add(ref Unsafe.AsRef(in _item_0_0), i) = value;
    }
<#} else {#>
    public T this[int i, int j]
    {
        get => Unsafe.Add(ref Unsafe.AsRef(in _item_0_0), i * Width + j);
        set => Unsafe.Add(ref Unsafe.AsRef(in _item_0_0), i * Width + j) = value;
    }
<#}#>

    public bool Equals(<#=typeName#> other) => AsSpan().SequenceEqual(other.AsSpan());

    public override bool Equals([NotNullWhen(true)] object obj) => obj is <#=typeName#> other && Equals(other);

    public Span<T> AsSpan() => MemoryMarshal.CreateSpan(ref Unsafe.AsRef(in _item_0_0), Count);

<# if (lanesA != 0) {#>

    public Span<T> AsSpan(int i) => MemoryMarshal.CreateSpan(ref Unsafe.Add(ref Unsafe.AsRef(in _item_0_0), i * Width), Width);

    public Span2D<T> AsSpan2D() => Span2D<T>.DangerousCreate(ref Unsafe.AsRef(in _item_0_0), Height, Width, 1);

    public static int Height => <#=lanesA#>;

    public static int Width => <#=lanesB#>;

    public static int Count => Height * Width;
<# } else {#>
    public static int Count => <#=lanesB#>;
<#}#>

<#foreach (var op in new string[] { }) {#>
    public static <#=typeName#> operator <#=op#>(<#=typeName#> left, <#=typeName#> right) 
    {
        <#=typeName#> res = default;
        var lhs = left.AsSpan();
        var rhs = right.AsSpan();
        var span = res.AsSpan();
        for (int i = 0; i < span.Length; i++)
        {
            span[i] = lhs[i] <#=op#> rhs[i];
        }
        return res;
    }

<#}#>

    public override string ToString()
    {
<# if (lanesA == 0) {#>
        return $"<{Nncase.Utilities.StringUtility.Join<T>(',', AsSpan())}>";
<#} else {#>
        var sb = new System.Text.StringBuilder();
        sb.Append("<");
        for (int i = 0; i < Height; i++)
        {
            sb.Append($"<{Nncase.Utilities.StringUtility.Join<T>(',', AsSpan(i))}>");
            if (i < Height - 1)
            {
                sb.Append(",");
            }
        }
        sb.Append(">");
        return sb.ToString();
<#}#>
    }
}

<#}#>