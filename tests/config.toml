name = 'default_config'
root = 'tests_output'
kmodel_name = 'test.kmodel'
desc_name = 'kmodel.desc'
dump_hist = false

[compile_opt]
preprocess = false
swapRB = false
input_type = 'uint8'
input_shape = [1, 224, 224, 3]
input_range = [0, 255]
input_file = ""
mean = [0, 0, 0]
std = [1, 1, 1]
input_layout = 'NHWC'
output_layout = 'NHWC'
model_layout = 'NHWC'
letterbox_value = 0
dump_asm = true
dump_ir = false
enable_profiling = false
shape_bucket_enable = false
shape_bucket_range_info = {}
shape_bucket_segments_count = 4
shape_bucket_fix_var_map = {}

[ptq_opt]
use_mix_quant = false
use_mse_quant_w = true
export_quant_scheme = false
export_weight_range_by_channel = true
dump_quant_error = false
dump_quant_error_symmetric_for_signed = true
quant_type = "uint8"
w_quant_type = "uint8"
# ['NoClip', 'Kld']
calibrate_method = 'NoClip'
# ['NoFineTuneWeights', 'UseSquant']
finetune_weights_method = 'NoFineTuneWeights'
input_mean = 0.5
input_std = 0.5
quant_scheme = ""
quant_scheme_strict_mode = false

[huggingface_options]
output_logits = true
output_hidden_states = false
num_layers = -1

[paged_attention_config]
block_size = 256
num_blocks = 16
max_sessions = 16
kv_type = "float32"
cache_layout = ["NumBlocks","NumLayers","KV","NumKVHeads","HeadDim","BlockSize"]
vectorized_axes = ["HeadDim"]
lanes = [32]
sharding_axes = ["NumBlocks"]
axis_policies = [[0]]
hierarchy = [1]

[infer_report_opt]
enabled = false
priority = 100
kind = 'N/A'
model_name = 'N/A'
report_name = 'infer_report.json'

[generator]
[generator.inputs]
# ['random', 'bin', 'image', 'constant_of_shape', 'text']
method = 'random'
number = 1
batch = 1

[generator.inputs.random]
args = false

[generator.inputs.bin]
# /path/to/bin directory
args = ''

[generator.inputs.image]
# /path/to/image directory
args = ''

[generator.inputs.constant_of_shape]
# shape
args = []

[generator.inputs.text]
# /path/to/prompt.txt directory
args = 'tests/llm/prompt.txt'

[generator.calibs]
method = 'random'
number = 1
batch = 1

[generator.calibs.random]
args = false

[generator.calibs.bin]
# /path/to/bin directory
args = ''

[generator.calibs.image]
# /path/to/image directory
args = ''

[generator.calibs.constant_of_shape]
# shape
args = []

[generator.calibs.text]
# /path/to/prompt.txt directory
args = 'tests/llm/prompt.txt'

[target]

[target.cpu]
eval = true
infer = true
similarity_name = 'cosine'

[target.cpu.mode.noptq]
enabled = true
threshold = 0.999

[target.cpu.mode.ptq]
enabled = false
threshold = 0.98

[target.cpu.target_options]
Vectorize = true
Hierarchies = [[1]]
HierarchyNames = "t"

[target.k510]
eval = true
infer = true
similarity_name = 'cosine'

[target.k510.mode.noptq]
enabled = false
threshold = 0.99

[target.k510.mode.ptq]
enabled = true
threshold = 0.98

[target.k230]
eval = true
infer = true
similarity_name = 'cosine'

[target.k230.mode.noptq]
enabled = false
threshold = 0.999

[target.k230.mode.ptq]
enabled = true
threshold = 0.96

[target.xpu]
eval = false
infer = true
similarity_name = 'cosine'

[target.xpu.mode.noptq]
enabled = true
threshold = 0.999

[target.xpu.mode.ptq]
enabled = false
threshold = 0.9

[target.xpu.target_options]
Hierarchies = [[1, 2, 8, 4, 4]]
HierarchyNames = "cdyxt"
HierarchySizes = [603979776, 1048576]
MemoryCapacities = [262144, 150994944]
MemoryBandWidths = [64, 32]
UnifiedMemoryArch = false
Vectorize = true
HierarchyKind = "nncase.HierarchyKind.SMT"
