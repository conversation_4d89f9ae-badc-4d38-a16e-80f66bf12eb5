# Copyright 2019-2021 Canaan Inc.
#
# Licensed under the Apache License, Version 2.0 (the "License");
# you may not use this file except in compliance with the License.
# You may obtain a copy of the License at
#
#     http://www.apache.org/licenses/LICENSE-2.0
#
# Unless required by applicable law or agreed to in writing, software
# distributed under the License is distributed on an "AS IS" BASIS,
# WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
# See the License for the specific language governing permissions and
# limitations under the License.
# pylint: disable=invalid-name, unused-argument, import-outside-toplevel

import pytest
import nncase


def test_cpu_options_setter(request):
    opt = nncase.NTTTargetOptions()
    opt.Vectorize = True
    opt.ModelName = "unknow"
    opt.Hierarchies = [[1], [2, 3]]
    opt.NocArch = nncase.NocArchitecture.CrossBar


if __name__ == "__main__":
    pytest.main(['-vv', __file__])
